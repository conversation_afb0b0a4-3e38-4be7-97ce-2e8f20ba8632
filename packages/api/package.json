{"name": "@another-nestjs-project/api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-kms": "^3.812.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^3.2.0", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/platform-express": "^11.0.1", "@vendia/serverless-express": "^4.12.6", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "nestjs-pino": "^4.1.0", "pino": "^9.0.0", "pino-http": "^10.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "@another-nestjs-project/database": "0.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s", "!**/*.spec.ts", "!**/*.interface.ts", "!**/*.d.ts", "!**/node_modules/**", "!**/dist/**"], "coverageDirectory": "../coverage", "coverageReporters": ["text", "lcov", "html"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}, "testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/test-setup.ts"], "moduleNameMapping": {"^@another-nestjs-project/database/(.*)$": "<rootDir>/../database/src/$1", "^@another-nestjs-project/database$": "<rootDir>/../database/src/index.ts"}}}