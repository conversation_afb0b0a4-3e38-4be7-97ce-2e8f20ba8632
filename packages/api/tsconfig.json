{"compilerOptions": {"module": "NodeNext", "target": "ES2023", "moduleResolution": "node16", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "strict": false, "strictNullChecks": false, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": true, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false}, "references": [{"path": "../database"}]}