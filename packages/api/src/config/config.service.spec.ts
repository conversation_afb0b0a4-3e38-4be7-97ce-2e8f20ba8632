import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppConfigService } from './config.service';
import configuration, { Environment, DatabaseConnectionType } from './configuration';

describe('AppConfigService', () => {
  let service: AppConfigService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [configuration],
          isGlobal: true,
        }),
      ],
      providers: [AppConfigService],
    }).compile();

    service = module.get<AppConfigService>(AppConfigService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Environment Configuration', () => {
    it('should return correct node environment', () => {
      jest.spyOn(configService, 'get').mockReturnValue(Environment.Development);
      expect(service.nodeEnv).toBe(Environment.Development);
    });

    it('should return correct port', () => {
      jest.spyOn(configService, 'get').mockReturnValue(3000);
      expect(service.port).toBe(3000);
    });

    it('should correctly identify development environment', () => {
      jest.spyOn(configService, 'get').mockReturnValue(Environment.Development);
      expect(service.isDevelopment).toBe(true);
      expect(service.isProduction).toBe(false);
      expect(service.isTest).toBe(false);
    });

    it('should correctly identify production environment', () => {
      jest.spyOn(configService, 'get').mockReturnValue(Environment.Production);
      expect(service.isDevelopment).toBe(false);
      expect(service.isProduction).toBe(true);
      expect(service.isTest).toBe(false);
    });
  });

  describe('Database Configuration', () => {
    it('should return database connection type', () => {
      jest.spyOn(configService, 'get').mockReturnValue(DatabaseConnectionType.Local);
      expect(service.databaseConnectionType).toBe(DatabaseConnectionType.Local);
    });

    it('should return local database configuration', () => {
      jest.spyOn(configService, 'get')
        .mockReturnValueOnce('localhost')
        .mockReturnValueOnce(5432)
        .mockReturnValueOnce('testuser')
        .mockReturnValueOnce('testdb');

      expect(service.localDatabaseHost).toBe('localhost');
      expect(service.localDatabasePort).toBe(5432);
      expect(service.localDatabaseUser).toBe('testuser');
      expect(service.localDatabaseName).toBe('testdb');
    });

    it('should return AWS configuration', () => {
      jest.spyOn(configService, 'get')
        .mockReturnValueOnce('us-east-1')
        .mockReturnValueOnce('arn:aws:rds:us-east-1:123456789012:cluster:test')
        .mockReturnValueOnce('arn:aws:secretsmanager:us-east-1:123456789012:secret:test')
        .mockReturnValueOnce('testdb');

      expect(service.awsRegion).toBe('us-east-1');
      expect(service.rdsResourceArn).toBe('arn:aws:rds:us-east-1:123456789012:cluster:test');
      expect(service.rdsSecretArn).toBe('arn:aws:secretsmanager:us-east-1:123456789012:secret:test');
      expect(service.rdsDatabaseName).toBe('testdb');
    });
  });

  describe('KMS Configuration', () => {
    it('should return KMS ARN', () => {
      const kmsArn = 'arn:aws:kms:us-east-1:123456789012:key/test';
      jest.spyOn(configService, 'get').mockReturnValue(kmsArn);
      expect(service.kmsArn).toBe(kmsArn);
    });
  });

  describe('Logging Configuration', () => {
    it('should return log level', () => {
      jest.spyOn(configService, 'get').mockReturnValue('debug');
      expect(service.logLevel).toBe('debug');
    });

    it('should return drizzle logger setting', () => {
      jest.spyOn(configService, 'get').mockReturnValue(true);
      expect(service.drizzleLogger).toBe(true);
    });
  });

  describe('Validation', () => {
    beforeEach(() => {
      jest.spyOn(service, 'databaseConnectionType', 'get')
        .mockReturnValue(DatabaseConnectionType.AwsDataApi);
    });

    it('should validate AWS configuration successfully', () => {
      jest.spyOn(service, 'awsRegion', 'get').mockReturnValue('us-east-1');
      jest.spyOn(service, 'rdsResourceArn', 'get').mockReturnValue('arn:aws:rds:test');
      jest.spyOn(service, 'rdsSecretArn', 'get').mockReturnValue('arn:aws:secretsmanager:test');
      jest.spyOn(service, 'rdsDatabaseName', 'get').mockReturnValue('testdb');

      expect(() => service.validateAwsConfiguration()).not.toThrow();
    });

    it('should throw error for missing AWS configuration', () => {
      jest.spyOn(service, 'awsRegion', 'get').mockReturnValue(undefined);
      jest.spyOn(service, 'rdsResourceArn', 'get').mockReturnValue(undefined);
      jest.spyOn(service, 'rdsSecretArn', 'get').mockReturnValue(undefined);
      jest.spyOn(service, 'rdsDatabaseName', 'get').mockReturnValue(undefined);

      expect(() => service.validateAwsConfiguration()).toThrow(
        'Missing required AWS configuration: awsRegion, rdsResourceArn, rdsSecretArn, rdsDatabaseName'
      );
    });

    it('should validate local configuration successfully', () => {
      jest.spyOn(service, 'databaseConnectionType', 'get')
        .mockReturnValue(DatabaseConnectionType.Local);
      jest.spyOn(service, 'localDatabaseHost', 'get').mockReturnValue('localhost');
      jest.spyOn(service, 'localDatabasePort', 'get').mockReturnValue(5432);
      jest.spyOn(service, 'localDatabaseUser', 'get').mockReturnValue('user');
      jest.spyOn(service, 'localDatabaseName', 'get').mockReturnValue('db');

      expect(() => service.validateLocalConfiguration()).not.toThrow();
    });
  });
});
