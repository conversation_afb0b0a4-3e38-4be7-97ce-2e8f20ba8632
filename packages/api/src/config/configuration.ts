import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON><PERSON>, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Transform } from 'class-transformer';

export enum Environment {
  Development = 'development',
  Production = 'production',
  Test = 'test',
}

export enum DatabaseConnectionType {
  Local = 'local',
  AwsDataApi = 'aws-data-api-pg',
}

export class EnvironmentVariables {
  @IsEnum(Environment)
  @IsNotEmpty()
  NODE_ENV: Environment = Environment.Development;

  @IsEnum(DatabaseConnectionType)
  @IsNotEmpty()
  DB_CONNECTION_TYPE: DatabaseConnectionType = DatabaseConnectionType.Local;

  // AWS RDS Configuration
  @IsOptional()
  @IsString()
  RDS_AWS_REGION?: string;

  @IsOptional()
  @IsString()
  RDS_RESOURCE_ARN?: string;

  @IsOptional()
  @IsString()
  RDS_SECRET_ARN?: string;

  @IsOptional()
  @IsString()
  RDS_DATABASE_NAME?: string;

  // Local Database Configuration
  @IsOptional()
  @IsString()
  LOCAL_DATABASE_HOST?: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(1)
  @Max(65535)
  LOCAL_DATABASE_PORT?: number;

  @IsOptional()
  @IsString()
  LOCAL_DATABASE_USER?: string;

  @IsOptional()
  @IsString()
  LOCAL_DATABASE_NAME?: string;

  @IsOptional()
  @IsString()
  LOCAL_DATABASE_PASSWORD?: string;

  // KMS Configuration
  @IsString()
  @IsNotEmpty()
  KMS_ARN!: string;

  // Logging Configuration
  @IsOptional()
  @IsString()
  LOG_LEVEL?: string = 'info';

  @IsOptional()
  @Transform(({ value }) => value === 'true')
  DRIZZLE_LOGGER?: boolean = false;

  // Application Configuration
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(1)
  @Max(65535)
  PORT?: number = 3000;
}

export default () => ({
  nodeEnv: process.env.NODE_ENV || Environment.Development,
  port: parseInt(process.env.PORT || '3000', 10),
  database: {
    connectionType: process.env.DB_CONNECTION_TYPE || DatabaseConnectionType.Local,
    aws: {
      region: process.env.RDS_AWS_REGION,
      resourceArn: process.env.RDS_RESOURCE_ARN,
      secretArn: process.env.RDS_SECRET_ARN,
      databaseName: process.env.RDS_DATABASE_NAME,
    },
    local: {
      host: process.env.LOCAL_DATABASE_HOST || '127.0.0.1',
      port: parseInt(process.env.LOCAL_DATABASE_PORT || '5432', 10),
      user: process.env.LOCAL_DATABASE_USER || 'admin',
      database: process.env.LOCAL_DATABASE_NAME || 'postgres',
      password: process.env.LOCAL_DATABASE_PASSWORD,
    },
  },
  kms: {
    arn: process.env.KMS_ARN,
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    drizzleLogger: process.env.DRIZZLE_LOGGER === 'true',
  },
});
