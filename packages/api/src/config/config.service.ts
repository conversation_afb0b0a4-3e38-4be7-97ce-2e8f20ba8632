import { Injectable } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { DatabaseConnectionType, Environment } from './configuration';

@Injectable()
export class AppConfigService {
  constructor(private configService: NestConfigService) {}

  get nodeEnv(): Environment {
    return this.configService.get<Environment>('nodeEnv')!;
  }

  get port(): number {
    return this.configService.get<number>('port')!;
  }

  get isDevelopment(): boolean {
    return this.nodeEnv === Environment.Development;
  }

  get isProduction(): boolean {
    return this.nodeEnv === Environment.Production;
  }

  get isTest(): boolean {
    return this.nodeEnv === Environment.Test;
  }

  // Database Configuration
  get databaseConnectionType(): DatabaseConnectionType {
    return this.configService.get<DatabaseConnectionType>('database.connectionType')!;
  }

  get awsRegion(): string | undefined {
    return this.configService.get<string>('database.aws.region');
  }

  get rdsResourceArn(): string | undefined {
    return this.configService.get<string>('database.aws.resourceArn');
  }

  get rdsSecretArn(): string | undefined {
    return this.configService.get<string>('database.aws.secretArn');
  }

  get rdsDatabaseName(): string | undefined {
    return this.configService.get<string>('database.aws.databaseName');
  }

  get localDatabaseHost(): string {
    return this.configService.get<string>('database.local.host')!;
  }

  get localDatabasePort(): number {
    return this.configService.get<number>('database.local.port')!;
  }

  get localDatabaseUser(): string {
    return this.configService.get<string>('database.local.user')!;
  }

  get localDatabaseName(): string {
    return this.configService.get<string>('database.local.database')!;
  }

  get localDatabasePassword(): string | undefined {
    return this.configService.get<string>('database.local.password');
  }

  // KMS Configuration
  get kmsArn(): string {
    return this.configService.get<string>('kms.arn')!;
  }

  // Logging Configuration
  get logLevel(): string {
    return this.configService.get<string>('logging.level')!;
  }

  get drizzleLogger(): boolean {
    return this.configService.get<boolean>('logging.drizzleLogger')!;
  }

  // Validation helpers
  validateAwsConfiguration(): void {
    if (this.databaseConnectionType === DatabaseConnectionType.AwsDataApi) {
      const requiredFields = [
        { key: 'awsRegion', value: this.awsRegion },
        { key: 'rdsResourceArn', value: this.rdsResourceArn },
        { key: 'rdsSecretArn', value: this.rdsSecretArn },
        { key: 'rdsDatabaseName', value: this.rdsDatabaseName },
      ];

      const missingFields = requiredFields
        .filter(field => !field.value)
        .map(field => field.key);

      if (missingFields.length > 0) {
        throw new Error(
          `Missing required AWS configuration: ${missingFields.join(', ')}`
        );
      }
    }
  }

  validateLocalConfiguration(): void {
    if (this.databaseConnectionType === DatabaseConnectionType.Local) {
      const requiredFields = [
        { key: 'localDatabaseHost', value: this.localDatabaseHost },
        { key: 'localDatabasePort', value: this.localDatabasePort },
        { key: 'localDatabaseUser', value: this.localDatabaseUser },
        { key: 'localDatabaseName', value: this.localDatabaseName },
      ];

      const missingFields = requiredFields
        .filter(field => !field.value)
        .map(field => field.key);

      if (missingFields.length > 0) {
        throw new Error(
          `Missing required local database configuration: ${missingFields.join(', ')}`
        );
      }
    }
  }
}
