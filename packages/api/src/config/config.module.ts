import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppConfigService } from './config.service';
import configuration, { EnvironmentVariables } from './configuration';
import { validateSync } from 'class-validator';
import { plainToClass } from 'class-transformer';

function validate(config: Record<string, unknown>) {
  const validatedConfig = plainToClass(EnvironmentVariables, config, {
    enableImplicitConversion: true,
  });

  const errors = validateSync(validatedConfig, {
    skipMissingProperties: false,
  });

  if (errors.length > 0) {
    const errorMessages = errors.map(error => {
      const constraints = error.constraints;
      return constraints ? Object.values(constraints).join(', ') : 'Unknown validation error';
    });
    throw new Error(`Configuration validation failed: ${errorMessages.join('; ')}`);
  }

  return validatedConfig;
}

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [configuration],
      isGlobal: true,
      validate,
      envFilePath: ['.env.local', '.env'],
      expandVariables: true,
    }),
  ],
  providers: [AppConfigService],
  exports: [AppConfigService],
})
export class AppConfigModule {}
