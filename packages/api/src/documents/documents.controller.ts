import {
  <PERSON>,
  Get,
  HttpException,
  HttpStatus,
  Inject,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import { DocumentsService } from './documents.service';
import { User } from '@another-nestjs-project/database/schema';

@Controller('documents')
export class DocumentsController {
  constructor(
    @Inject(DocumentsService)
    private readonly documentsService: DocumentsService,
  ) {}

  @Get()
  async getAll(@Req() req: Request): Promise<any> {
    if (!req.user) {
      throw new HttpException('Cannot find user', HttpStatus.UNAUTHORIZED);
    }
    return {
      documents: [await this.documentsService.getAll(req.user as User)],
      statusCode: HttpStatus.OK,
    };
  }
}
