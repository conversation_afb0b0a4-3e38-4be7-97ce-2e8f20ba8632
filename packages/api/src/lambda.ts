import 'reflect-metadata'; // <-- ADD THIS LINE AT THE VERY TOP
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { AppService } from './app.service'; // Import AppService to use its type
import { configure as serverlessExpress } from '@vendia/serverless-express';
import { INestApplication, ValidationPipe, Logger } from '@nestjs/common';
import { Context, Handler } from 'aws-lambda';
import { ResponseTransformInterceptor } from './common/interceptors/response.interceptor';

// Declare a cache variable to store the configured server
let cachedServer: Handler;

async function bootstrap(): Promise<INestApplication> {
  const logger = new Logger('Lambda Bootstrap');

  logger.log('Bootstrapping Nest application...');
  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
  });

  // Use Pino logger
  app.useLogger(app.get(Logger));

  logger.log('Nest application instance created.');

  app.useGlobalInterceptors(new ResponseTransformInterceptor());

  // Apply global pipes with enhanced validation
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
  logger.log('Global pipes applied.');

  await app.init();
  logger.log('Nest application initialized.');

  try {
    const serviceFromContext = app.get(AppService);
    console.log(
      '[Lambda Bootstrap] app.get(AppService) resolved:',
      !!serviceFromContext,
      serviceFromContext,
    );
    if (serviceFromContext) {
      console.log(
        '[Lambda Bootstrap] serviceFromContext.getHello():',
        serviceFromContext.getHello(),
      );
    }
  } catch (e) {
    console.error(
      '[Lambda Bootstrap] FAILED to get AppService from app context:',
      e,
    );
  }

  return app;
}

// Export the handler function for AWS Lambda
export const handler: Handler = async (
  event: any,
  context: Context,
  callback: any,
) => {
  // If the server is not cached, bootstrap and cache it
  if (!cachedServer) {
    const nestApp = await bootstrap();
    // Use 'express' as the http adapter name for NestJS
    cachedServer = serverlessExpress({
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      app: nestApp.getHttpAdapter().getInstance(),
    });
  }

  // Pass the event and context to the cached server
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-argument
  return cachedServer(event, context, callback);
};
