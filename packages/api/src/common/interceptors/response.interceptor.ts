/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  HttpStatus,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { StandardResponse } from '../interfaces/response.interface'; // Your standard interface

@Injectable()
export class ResponseTransformInterceptor<T>
  implements NestInterceptor<T, StandardResponse<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<StandardResponse<T>> {
    const httpContext = context.switchToHttp();
    const response = httpContext.getResponse(); // Get the native response object
    const request = httpContext.getRequest();

    return next.handle().pipe(
      map((data) => {
        if (data && 'statusCode' in data) {
          response.status(data.statusCode);
          delete data.statusCode;
        }

        // If data is already in StandardResponse format then pass it through
        if (data && typeof data === 'object' && 'message' in data) {
          return data;
        }

        // Determine statusCode - default to 200 for GET, 201 for POST, etc.
        // Or you can explicitly set it in controllers if needed and retrieve here
        let statusCode = response.statusCode; // The status code set by NestJS or controller
        if (request.method === 'POST' && statusCode === HttpStatus.OK) {
          statusCode = HttpStatus.CREATED; // Conventionally, POST that creates returns 201
        }

        return {
          message: 'OK', // Or a more dynamic message
          data: data,
        };
      }),
    );
  }
}
