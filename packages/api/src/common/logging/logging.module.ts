import { Module } from '@nestjs/common';
import { LoggerModule } from 'nestjs-pino';
import { AppConfigService } from '../../config/config.service';

@Module({
  imports: [
    LoggerModule.forRootAsync({
      inject: [AppConfigService],
      useFactory: (configService: AppConfigService) => ({
        pinoHttp: {
          level: configService.logLevel,
          transport: configService.isDevelopment
            ? {
                target: 'pino-pretty',
                options: {
                  colorize: true,
                  translateTime: 'SYS:standard',
                  ignore: 'pid,hostname',
                },
              }
            : undefined,
          formatters: {
            level: (label: string) => ({ level: label }),
          },
          serializers: {
            req: (req: any) => ({
              method: req.method,
              url: req.url,
              headers: {
                'user-agent': req.headers['user-agent'],
                'content-type': req.headers['content-type'],
                authorization: req.headers.authorization ? '[REDACTED]' : undefined,
              },
              correlationId: req.correlationId,
            }),
            res: (res: any) => ({
              statusCode: res.statusCode,
              headers: {
                'content-type': res.headers['content-type'],
                'content-length': res.headers['content-length'],
              },
            }),
          },
          customProps: (req: any) => ({
            correlationId: req.correlationId || req.headers['x-correlation-id'],
          }),
          redact: {
            paths: [
              'req.headers.authorization',
              'req.headers.cookie',
              'req.body.password',
              'req.body.token',
              'res.headers["set-cookie"]',
            ],
            censor: '[REDACTED]',
          },
        },
      }),
    }),
  ],
  exports: [LoggerModule],
})
export class AppLoggingModule {}
