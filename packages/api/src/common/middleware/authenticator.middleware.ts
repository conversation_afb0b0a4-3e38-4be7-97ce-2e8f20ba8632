import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NestMiddleware,
  Logger,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { KMSClient, GetPublicKeyCommand } from '@aws-sdk/client-kms';
import { JwtService } from '@nestjs/jwt';
import * as crypto from 'crypto';
import { findUserByEstendioId } from '@another-nestjs-project/database/queries/users';
import { JwtPayload } from '../interfaces/jwt.interface';
import { AppConfigService } from '../../config/config.service';

@Injectable()
export class AuthenticatorMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AuthenticatorMiddleware.name);
  private kmsClient: KMSClient;

  constructor(
    @Inject(JwtService) private readonly jwtService: JwtService,
    private readonly configService: AppConfigService,
  ) {
    this.kmsClient = new KMSClient({
      region: this.configService.awsRegion,
    });
  }

  private async getKmsPublicKey(): Promise<string | Buffer<ArrayBufferLike>> {
    try {
      const command = new GetPublicKeyCommand({ KeyId: this.configService.kmsArn });
      const response = await this.kmsClient.send(command);

      if (!response.PublicKey) {
        this.logger.error('Public key not found in KMS response');
        throw new HttpException(
          'Public key not found in KMS response',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      // response.PublicKey is a Uint8Array in AWS SDK v3.
      // Convert DER (binary) public key to PEM format.
      const publicKeyDer = Buffer.from(response.PublicKey);

      const publicKeyObject = crypto.createPublicKey({
        key: publicKeyDer,
        format: 'der',
        type: 'spki', // SubjectPublicKeyInfo format
      });

      const publicKeyPem = publicKeyObject.export({
        format: 'pem',
        type: 'spki',
      });

      return publicKeyPem;
    } catch (error) {
      this.logger.error('Error fetching or converting KMS public key', error);
      throw new HttpException(
        'Failed to retrieve public key for JWT verification.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async use(req: Request, res: Response, next: NextFunction): Promise<void> {
    if (!req.headers.authorization) {
      throw new HttpException(
        'Invalid Authorisation header',
        HttpStatus.UNAUTHORIZED,
      );
    }

    const splitAuthHeader = req.headers.authorization.split(' ');
    if (splitAuthHeader.length != 2) {
      throw new HttpException(
        'Invalid authorization header',
        HttpStatus.UNAUTHORIZED,
      );
    }

    if (splitAuthHeader[0] !== 'Bearer') {
      throw new HttpException(
        'Invalid authorization header',
        HttpStatus.UNAUTHORIZED,
      );
    }

    const ticket = splitAuthHeader[1];

    const publicKeyPem = await this.getKmsPublicKey();

    try {
      const decodedTicket: JwtPayload = this.jwtService.verify(ticket, {
        publicKey: publicKeyPem,
        algorithms: ['PS256'],
      });

      const user = await findUserByEstendioId(decodedTicket.userId);
      if (!user) {
        throw new HttpException(
          'Invalid authorization header',
          HttpStatus.UNAUTHORIZED,
        );
      }
      req.user = user;
      this.logger.debug(`User authenticated: ${user.estendioId}`);
    } catch (e) {
      this.logger.warn('JWT verification failed', { error: e });
      throw new HttpException(
        'Invalid or expired token',
        HttpStatus.UNAUTHORIZED,
      );
    }

    next();
  }
}
