interface JwtPayloadPermission {
  action: string;
  subject: any[];
}

interface JwtFeatures {
  id: string;
  config: any[];
}

interface JwtPayloadLicence {
  id: string;
  accountId: string;
  accountName: string;
  category: string;
}

export interface JwtPayload {
  userId: string;
  iss: string;
  nbf: number;
  exp: number;
  trackingId: string;
  permissions: JwtPayloadPermission[];
  features: JwtFeatures;
  license: JwtPayloadLicence;
}
