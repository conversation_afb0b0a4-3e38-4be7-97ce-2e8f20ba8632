import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus, ArgumentsHost } from '@nestjs/common';
import { GlobalExceptionFilter } from './global-exception.filter';
import { Request, Response } from 'express';

describe('GlobalExceptionFilter', () => {
  let filter: GlobalExceptionFilter;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockArgumentsHost: Partial<ArgumentsHost>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GlobalExceptionFilter],
    }).compile();

    filter = module.get<GlobalExceptionFilter>(GlobalExceptionFilter);

    mockRequest = {
      url: '/test',
      method: 'GET',
      get: jest.fn().mockReturnValue('test-user-agent'),
      ip: '127.0.0.1',
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: () => mockRequest,
        getResponse: () => mockResponse,
      }),
    };
  });

  it('should be defined', () => {
    expect(filter).toBeDefined();
  });

  describe('HTTP Exception Handling', () => {
    it('should handle HttpException with string response', () => {
      const exception = new HttpException('Test error', HttpStatus.BAD_REQUEST);

      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Request failed',
          data: null,
          error: expect.objectContaining({
            type: 'HttpException',
            message: 'Test error',
            timestamp: expect.any(String),
            path: '/test',
            correlationId: expect.any(String),
          }),
        })
      );
    });

    it('should handle HttpException with object response', () => {
      const exceptionResponse = {
        error: 'ValidationError',
        message: 'Validation failed',
        details: { field: 'required' },
      };
      const exception = new HttpException(exceptionResponse, HttpStatus.BAD_REQUEST);

      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.objectContaining({
            type: 'ValidationError',
            message: 'Validation failed',
            details: { field: 'required' },
          }),
        })
      );
    });
  });

  describe('Generic Error Handling', () => {
    it('should handle generic Error instances', () => {
      const error = new Error('Generic error');

      filter.catch(error, mockArgumentsHost as ArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.objectContaining({
            type: 'Error',
            message: 'Generic error',
          }),
        })
      );
    });

    it('should handle custom error types', () => {
      class CustomError extends Error {
        constructor(message: string) {
          super(message);
          this.name = 'CustomError';
        }
      }

      const error = new CustomError('Custom error message');

      filter.catch(error, mockArgumentsHost as ArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.objectContaining({
            type: 'CustomError',
            message: 'Custom error message',
          }),
        })
      );
    });
  });

  describe('Unknown Exception Handling', () => {
    it('should handle unknown exception types', () => {
      const unknownException = 'string exception';

      filter.catch(unknownException, mockArgumentsHost as ArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.objectContaining({
            type: 'UnknownError',
            message: 'An unexpected error occurred',
          }),
        })
      );
    });
  });

  describe('Correlation ID Generation', () => {
    it('should generate unique correlation IDs', () => {
      const exception1 = new HttpException('Test 1', HttpStatus.BAD_REQUEST);
      const exception2 = new HttpException('Test 2', HttpStatus.BAD_REQUEST);

      filter.catch(exception1, mockArgumentsHost as ArgumentsHost);
      const firstCall = (mockResponse.json as jest.Mock).mock.calls[0][0];

      filter.catch(exception2, mockArgumentsHost as ArgumentsHost);
      const secondCall = (mockResponse.json as jest.Mock).mock.calls[1][0];

      expect(firstCall.error.correlationId).not.toBe(secondCall.error.correlationId);
    });

    it('should add correlation ID to request', () => {
      const exception = new HttpException('Test', HttpStatus.BAD_REQUEST);

      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      expect((mockRequest as any).correlationId).toBeDefined();
      expect(typeof (mockRequest as any).correlationId).toBe('string');
    });
  });

  describe('Response Structure', () => {
    it('should include all required fields in error response', () => {
      const exception = new HttpException('Test error', HttpStatus.BAD_REQUEST);

      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      const responseCall = (mockResponse.json as jest.Mock).mock.calls[0][0];
      
      expect(responseCall).toHaveProperty('message', 'Request failed');
      expect(responseCall).toHaveProperty('data', null);
      expect(responseCall).toHaveProperty('error');
      expect(responseCall.error).toHaveProperty('type');
      expect(responseCall.error).toHaveProperty('message');
      expect(responseCall.error).toHaveProperty('timestamp');
      expect(responseCall.error).toHaveProperty('path');
      expect(responseCall.error).toHaveProperty('correlationId');
    });

    it('should include timestamp in ISO format', () => {
      const exception = new HttpException('Test error', HttpStatus.BAD_REQUEST);

      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      const responseCall = (mockResponse.json as jest.Mock).mock.calls[0][0];
      const timestamp = responseCall.error.timestamp;
      
      expect(() => new Date(timestamp)).not.toThrow();
      expect(new Date(timestamp).toISOString()).toBe(timestamp);
    });
  });
});
