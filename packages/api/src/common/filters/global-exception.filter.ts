import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { StandardResponse } from '../interfaces/response.interface';

export interface ErrorResponse extends StandardResponse<null> {
  error: {
    type: string;
    message: string;
    details?: any;
    timestamp: string;
    path: string;
    correlationId?: string;
  };
}

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const correlationId = this.generateCorrelationId();
    
    // Add correlation ID to request for logging
    (request as any).correlationId = correlationId;

    let status: HttpStatus;
    let errorType: string;
    let message: string;
    let details: any;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'string') {
        errorType = 'HttpException';
        message = exceptionResponse;
      } else if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
        const responseObj = exceptionResponse as any;
        errorType = responseObj.error || 'HttpException';
        message = responseObj.message || exception.message;
        details = responseObj.details;
      } else {
        errorType = 'HttpException';
        message = exception.message;
      }
    } else if (exception instanceof Error) {
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      errorType = exception.constructor.name;
      message = exception.message;
      
      // Log stack trace for server errors
      this.logger.error(
        `Unhandled exception: ${message}`,
        exception.stack,
        {
          correlationId,
          path: request.url,
          method: request.method,
          userAgent: request.get('User-Agent'),
          ip: request.ip,
        }
      );
    } else {
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      errorType = 'UnknownError';
      message = 'An unexpected error occurred';
      
      this.logger.error(
        `Unknown exception type: ${typeof exception}`,
        String(exception),
        {
          correlationId,
          path: request.url,
          method: request.method,
        }
      );
    }

    const errorResponse: ErrorResponse = {
      message: 'Request failed',
      data: null,
      error: {
        type: errorType,
        message,
        details,
        timestamp: new Date().toISOString(),
        path: request.url,
        correlationId,
      },
    };

    // Log the error with appropriate level
    if (status >= 500) {
      this.logger.error(
        `${request.method} ${request.url} - ${status} - ${message}`,
        {
          correlationId,
          status,
          errorType,
          userAgent: request.get('User-Agent'),
          ip: request.ip,
        }
      );
    } else if (status >= 400) {
      this.logger.warn(
        `${request.method} ${request.url} - ${status} - ${message}`,
        {
          correlationId,
          status,
          errorType,
          userAgent: request.get('User-Agent'),
          ip: request.ip,
        }
      );
    }

    response.status(status).json(errorResponse);
  }

  private generateCorrelationId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
