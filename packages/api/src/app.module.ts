import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DocumentsController } from './documents/documents.controller';
import { DocumentsService } from './documents/documents.service';
import { AuthenticatorMiddleware } from './common/middleware/authenticator.middleware';
import { JwtService } from '@nestjs/jwt';
import { AppConfigModule } from './config/config.module';
import { AppLoggingModule } from './common/logging/logging.module';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';

@Module({
  imports: [AppConfigModule, AppLoggingModule],
  controllers: [AppController, DocumentsController],
  providers: [
    AppService,
    DocumentsService,
    JwtService,
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AuthenticatorMiddleware).forRoutes('documents');
  }
}
