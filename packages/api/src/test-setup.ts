import 'reflect-metadata';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.DB_CONNECTION_TYPE = 'local';
process.env.LOCAL_DATABASE_HOST = 'localhost';
process.env.LOCAL_DATABASE_PORT = '5432';
process.env.LOCAL_DATABASE_USER = 'testuser';
process.env.LOCAL_DATABASE_NAME = 'testdb';
process.env.KMS_ARN = 'arn:aws:kms:us-east-1:123456789012:key/test-key-id';
process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests

// Global test utilities
global.console = {
  ...console,
  // Suppress console.log in tests unless explicitly needed
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Setup global test timeout
jest.setTimeout(30000);

// Mock AWS SDK clients for testing
jest.mock('@aws-sdk/client-kms', () => ({
  KMSClient: jest.fn().mockImplementation(() => ({
    send: jest.fn(),
  })),
  GetPublicKeyCommand: jest.fn(),
}));

jest.mock('@aws-sdk/client-rds-data', () => ({
  RDSDataClient: jest.fn().mockImplementation(() => ({
    send: jest.fn(),
  })),
}));

// Mock database queries - will be mocked in individual test files as needed

// Reset all mocks after each test
afterEach(() => {
  jest.clearAllMocks();
});
