TN:
SF:src/app.controller.ts
FN:6,(anonymous_4)
FN:9,(anonymous_5)
FNF:2
FNH:2
FNDA:3,(anonymous_4)
FNDA:2,(anonymous_5)
DA:1,1
DA:2,1
DA:5,1
DA:6,3
DA:9,1
DA:10,2
LF:6
LH:6
BRF:0
BRH:0
end_of_record
TN:
SF:src/app.module.ts
FN:27,(anonymous_1)
FNF:1
FNH:0
FNDA:0,(anonymous_1)
DA:1,0
DA:2,0
DA:3,0
DA:4,0
DA:5,0
DA:6,0
DA:7,0
DA:8,0
DA:9,0
DA:10,0
DA:11,0
DA:26,0
DA:28,0
LF:13
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/app.service.ts
FN:5,(anonymous_1)
FNF:1
FNH:1
FNDA:1,(anonymous_1)
DA:1,1
DA:4,1
DA:6,1
LF:3
LH:3
BRF:0
BRH:0
end_of_record
TN:
SF:src/lambda.ts
FN:13,bootstrap
FN:68,(anonymous_1)
FNF:2
FNH:0
FNDA:0,bootstrap
FNDA:0,(anonymous_1)
DA:1,0
DA:2,0
DA:3,0
DA:4,0
DA:5,0
DA:6,0
DA:8,0
DA:14,0
DA:16,0
DA:17,0
DA:22,0
DA:24,0
DA:26,0
DA:29,0
DA:39,0
DA:41,0
DA:42,0
DA:44,0
DA:45,0
DA:46,0
DA:51,0
DA:52,0
DA:58,0
DA:64,0
DA:68,0
DA:74,0
DA:75,0
DA:77,0
DA:85,0
LF:29
LH:0
BRDA:51,0,0,0
BRDA:74,1,0,0
BRF:2
BRH:0
end_of_record
TN:
SF:src/main.ts
FN:4,bootstrap
FNF:1
FNH:0
FNDA:0,bootstrap
DA:1,0
DA:2,0
DA:5,0
DA:6,0
DA:8,0
LF:5
LH:0
BRDA:6,0,0,0
BRDA:6,0,1,0
BRF:2
BRH:0
end_of_record
TN:
SF:src/common/filters/global-exception.filter.ts
FN:27,(anonymous_1)
FN:132,(anonymous_2)
FNF:2
FNH:2
FNDA:10,(anonymous_1)
FNDA:10,(anonymous_2)
DA:1,1
DA:24,1
DA:25,10
DA:28,10
DA:29,10
DA:30,10
DA:32,10
DA:35,10
DA:42,10
DA:43,7
DA:44,7
DA:46,7
DA:47,6
DA:48,6
DA:49,1
DA:50,1
DA:51,1
DA:52,1
DA:53,1
DA:55,0
DA:56,0
DA:58,3
DA:59,2
DA:60,2
DA:61,2
DA:64,2
DA:76,1
DA:77,1
DA:78,1
DA:80,1
DA:91,10
DA:105,10
DA:106,3
DA:116,7
DA:117,7
DA:129,10
DA:133,10
LF:37
LH:35
BRDA:42,0,0,7
BRDA:42,0,1,3
BRDA:46,1,0,6
BRDA:46,1,1,1
BRDA:49,2,0,1
BRDA:49,2,1,0
BRDA:49,3,0,1
BRDA:49,3,1,1
BRDA:51,4,0,1
BRDA:51,4,1,0
BRDA:52,5,0,1
BRDA:52,5,1,0
BRDA:58,6,0,2
BRDA:58,6,1,1
BRDA:105,7,0,3
BRDA:105,7,1,7
BRDA:116,8,0,7
BRF:17
BRH:14
end_of_record
TN:
SF:src/common/interceptors/response.interceptor.ts
FN:20,(anonymous_1)
FN:29,(anonymous_2)
FNF:2
FNH:0
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
DA:5,0
DA:13,0
DA:17,0
DA:24,0
DA:25,0
DA:26,0
DA:28,0
DA:30,0
DA:31,0
DA:32,0
DA:36,0
DA:37,0
DA:42,0
DA:43,0
DA:44,0
DA:47,0
LF:16
LH:0
BRDA:30,0,0,0
BRDA:30,1,0,0
BRDA:30,1,1,0
BRDA:36,2,0,0
BRDA:36,3,0,0
BRDA:36,3,1,0
BRDA:36,3,2,0
BRDA:43,4,0,0
BRDA:43,5,0,0
BRDA:43,5,1,0
BRF:10
BRH:0
end_of_record
TN:
SF:src/common/logging/logging.module.ts
FN:9,(anonymous_1)
FN:23,(anonymous_2)
FN:26,(anonymous_3)
FN:36,(anonymous_4)
FN:44,(anonymous_5)
FNF:5
FNH:0
FNDA:0,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
DA:1,0
DA:2,0
DA:3,0
DA:9,0
DA:23,0
DA:26,0
DA:36,0
DA:44,0
DA:63,0
LF:9
LH:0
BRDA:12,0,0,0
BRDA:12,0,1,0
BRDA:32,1,0,0
BRDA:32,1,1,0
BRDA:45,2,0,0
BRDA:45,2,1,0
BRF:6
BRH:0
end_of_record
TN:
SF:src/common/middleware/authenticator.middleware.ts
FN:22,(anonymous_13)
FN:31,(anonymous_14)
FN:69,(anonymous_15)
FNF:3
FNH:0
FNDA:0,(anonymous_13)
FNDA:0,(anonymous_14)
FNDA:0,(anonymous_15)
DA:1,0
DA:10,0
DA:11,0
DA:12,0
DA:13,0
DA:15,0
DA:18,0
DA:19,0
DA:23,0
DA:24,0
DA:26,0
DA:32,0
DA:33,0
DA:34,0
DA:36,0
DA:37,0
DA:38,0
DA:46,0
DA:48,0
DA:54,0
DA:59,0
DA:61,0
DA:62,0
DA:70,0
DA:71,0
DA:77,0
DA:78,0
DA:79,0
DA:85,0
DA:86,0
DA:92,0
DA:94,0
DA:96,0
DA:97,0
DA:102,0
DA:103,0
DA:104,0
DA:109,0
DA:110,0
DA:112,0
DA:113,0
DA:119,0
LF:42
LH:0
BRDA:36,0,0,0
BRDA:70,1,0,0
BRDA:78,2,0,0
BRDA:85,3,0,0
BRDA:103,4,0,0
BRF:5
BRH:0
end_of_record
TN:
SF:src/config/config.module.ts
FN:8,validate
FN:18,(anonymous_11)
FNF:2
FNH:0
FNDA:0,validate
FNDA:0,(anonymous_11)
DA:1,0
DA:2,0
DA:3,0
DA:4,0
DA:5,0
DA:6,0
DA:9,0
DA:13,0
DA:17,0
DA:18,0
DA:19,0
DA:20,0
DA:22,0
DA:25,0
DA:41,0
LF:15
LH:0
BRDA:17,0,0,0
BRDA:20,1,0,0
BRDA:20,1,1,0
BRF:3
BRH:0
end_of_record
TN:
SF:src/config/config.service.ts
FN:7,(anonymous_2)
FN:9,(anonymous_3)
FN:13,(anonymous_4)
FN:17,(anonymous_5)
FN:21,(anonymous_6)
FN:25,(anonymous_7)
FN:30,(anonymous_8)
FN:34,(anonymous_9)
FN:38,(anonymous_10)
FN:42,(anonymous_11)
FN:46,(anonymous_12)
FN:50,(anonymous_13)
FN:54,(anonymous_14)
FN:58,(anonymous_15)
FN:62,(anonymous_16)
FN:66,(anonymous_17)
FN:71,(anonymous_18)
FN:76,(anonymous_19)
FN:80,(anonymous_20)
FN:85,(anonymous_21)
FN:95,(anonymous_22)
FN:96,(anonymous_23)
FN:106,(anonymous_24)
FN:116,(anonymous_25)
FN:117,(anonymous_26)
FNF:25
FNH:23
FNDA:14,(anonymous_2)
FNDA:7,(anonymous_3)
FNDA:1,(anonymous_4)
FNDA:2,(anonymous_5)
FNDA:2,(anonymous_6)
FNDA:2,(anonymous_7)
FNDA:1,(anonymous_8)
FNDA:1,(anonymous_9)
FNDA:1,(anonymous_10)
FNDA:1,(anonymous_11)
FNDA:1,(anonymous_12)
FNDA:1,(anonymous_13)
FNDA:1,(anonymous_14)
FNDA:1,(anonymous_15)
FNDA:1,(anonymous_16)
FNDA:0,(anonymous_17)
FNDA:1,(anonymous_18)
FNDA:1,(anonymous_19)
FNDA:1,(anonymous_20)
FNDA:2,(anonymous_21)
FNDA:8,(anonymous_22)
FNDA:4,(anonymous_23)
FNDA:1,(anonymous_24)
FNDA:4,(anonymous_25)
FNDA:0,(anonymous_26)
DA:1,1
DA:2,1
DA:3,1
DA:6,1
DA:7,14
DA:10,7
DA:14,1
DA:18,2
DA:22,2
DA:26,2
DA:31,1
DA:35,1
DA:39,1
DA:43,1
DA:47,1
DA:51,1
DA:55,1
DA:59,1
DA:63,1
DA:67,0
DA:72,1
DA:77,1
DA:81,1
DA:86,2
DA:87,2
DA:94,2
DA:95,8
DA:96,4
DA:98,2
DA:99,1
DA:107,1
DA:108,1
DA:115,1
DA:116,4
DA:117,0
DA:119,1
DA:120,0
LF:37
LH:34
BRDA:86,0,0,2
BRDA:98,1,0,1
BRDA:107,2,0,1
BRDA:119,3,0,0
BRF:4
BRH:3
end_of_record
TN:
SF:src/config/configuration.ts
FN:4,(anonymous_2)
FN:10,(anonymous_3)
FN:47,(anonymous_4)
FN:76,(anonymous_5)
FN:81,(anonymous_6)
FN:88,(anonymous_7)
FNF:6
FNH:3
FNDA:1,(anonymous_2)
FNDA:1,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:14,(anonymous_7)
DA:1,1
DA:2,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:10,1
DA:11,1
DA:12,1
DA:15,1
DA:18,1
DA:22,1
DA:27,1
DA:31,1
DA:35,1
DA:39,1
DA:44,1
DA:47,0
DA:51,1
DA:55,1
DA:59,1
DA:63,1
DA:68,1
DA:73,1
DA:76,0
DA:77,1
DA:81,0
DA:85,1
DA:88,14
LF:29
LH:26
BRDA:4,0,0,1
BRDA:4,0,1,1
BRDA:10,1,0,1
BRDA:10,1,1,1
BRDA:89,2,0,14
BRDA:89,2,1,0
BRDA:90,3,0,14
BRDA:90,3,1,14
BRDA:92,4,0,14
BRDA:92,4,1,0
BRDA:100,5,0,14
BRDA:100,5,1,0
BRDA:101,6,0,14
BRDA:101,6,1,0
BRDA:102,7,0,14
BRDA:102,7,1,0
BRDA:103,8,0,14
BRDA:103,8,1,0
BRDA:111,9,0,14
BRDA:111,9,1,0
BRF:20
BRH:13
end_of_record
