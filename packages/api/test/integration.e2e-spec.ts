import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import request from 'supertest';
import { AppModule } from '../src/app.module';
import { AppConfigService } from '../src/config/config.service';
import { GlobalExceptionFilter } from '../src/common/filters/global-exception.filter';
import { ResponseTransformInterceptor } from '../src/common/interceptors/response.interceptor';

describe('Integration Tests (e2e)', () => {
  let app: INestApplication;
  let configService: AppConfigService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    configService = app.get<AppConfigService>(AppConfigService);

    // Apply the same configuration as in lambda.ts
    app.useGlobalInterceptors(new ResponseTransformInterceptor());
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Configuration Service', () => {
    it('should load configuration correctly', () => {
      expect(configService).toBeDefined();
      expect(configService.nodeEnv).toBeDefined();
      expect(configService.port).toBeDefined();
      expect(configService.databaseConnectionType).toBeDefined();
    });

    it('should have correct local database configuration', () => {
      expect(configService.databaseConnectionType).toBe('local');
      expect(configService.localDatabaseHost).toBe('127.0.0.1');
      expect(configService.localDatabasePort).toBe(5434);
      expect(configService.localDatabaseUser).toBe('admin');
      expect(configService.localDatabaseName).toBe('Tailov3');
    });

    it('should validate local configuration without errors', () => {
      expect(() => configService.validateLocalConfiguration()).not.toThrow();
    });
  });

  describe('Application Health', () => {
    it('/ (GET) should return Hello World', () => {
      return request(app.getHttpServer())
        .get('/')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('message', 'OK');
          expect(res.body).toHaveProperty('data', 'Hello World!');
        });
    });

    it('should handle 404 errors gracefully', () => {
      return request(app.getHttpServer())
        .get('/non-existent-route')
        .expect(404)
        .expect((res) => {
          expect(res.body).toHaveProperty('message', 'Request failed');
          expect(res.body).toHaveProperty('error');
          expect(res.body.error).toHaveProperty('type');
          expect(res.body.error).toHaveProperty('timestamp');
          expect(res.body.error).toHaveProperty('correlationId');
        });
    });
  });

  describe('Authentication Middleware', () => {
    it('should reject requests without authorization header', () => {
      return request(app.getHttpServer())
        .get('/documents')
        .expect(401)
        .expect((res) => {
          expect(res.body).toHaveProperty('message', 'Request failed');
          expect(res.body).toHaveProperty('error');
          expect(res.body.error).toHaveProperty('message', 'Invalid Authorisation header');
        });
    });

    it('should reject requests with invalid authorization header format', () => {
      return request(app.getHttpServer())
        .get('/documents')
        .set('Authorization', 'InvalidFormat')
        .expect(401)
        .expect((res) => {
          expect(res.body).toHaveProperty('message', 'Request failed');
          expect(res.body).toHaveProperty('error');
          expect(res.body.error).toHaveProperty('message', 'Invalid authorization header');
        });
    });

    it('should reject requests with non-Bearer authorization', () => {
      return request(app.getHttpServer())
        .get('/documents')
        .set('Authorization', 'Basic dGVzdDp0ZXN0')
        .expect(401)
        .expect((res) => {
          expect(res.body).toHaveProperty('message', 'Request failed');
          expect(res.body).toHaveProperty('error');
          expect(res.body.error).toHaveProperty('message', 'Invalid authorization header');
        });
    });
  });

  describe('Response Transformation', () => {
    it('should transform successful responses to standard format', () => {
      return request(app.getHttpServer())
        .get('/')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('message');
          expect(res.body).toHaveProperty('data');
          expect(res.body).not.toHaveProperty('error');
        });
    });
  });

  describe('Validation', () => {
    it('should validate request data (if applicable)', async () => {
      // This test would be more meaningful with actual endpoints that accept data
      // For now, we're testing that the validation pipe is properly configured
      const response = await request(app.getHttpServer())
        .get('/')
        .expect(200);

      expect(response.body).toBeDefined();
    });
  });
});
