import "dotenv/config";
import { defineConfig } from "drizzle-kit";
import * as dotenv from "dotenv";
import path = require("path");
dotenv.config({
  path: path.join(__dirname, "..", "..", ".env"),
});
export default defineConfig({
  out: "./src/migrations",
  schema: ["./src/schema/*.ts"],
  dialect: "postgresql",
  dbCredentials: {
    host: process.env.LOCAL_DATABASE_HOST || "127.0.0.1",
    port: Number(process.env.LOCAL_DATABASE_PORT) || 5434,
    user: process.env.LOCAL_DATABASE_USER || "admin",
    password: process.env.LOCAL_DATABASE_PASSWORD || "Qwerty1234",
    database: process.env.LOCAL_DATABASE_NAME || "Tailov3",
    ssl: false,
  },
});
