{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public.kysely_migration": {"name": "kysely_migration", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "timestamp": {"name": "timestamp", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.kysely_migration_lock": {"name": "kysely_migration_lock", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "is_locked": {"name": "is_locked", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "estendio_id": {"name": "estendio_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "NULL"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "modified_at": {"name": "modified_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"user_estendio_id_index": {"name": "user_estendio_id_index", "columns": [{"expression": "estendio_id", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_estendio_id_key": {"columns": ["estendio_id"], "nullsNotDistinct": false, "name": "users_estendio_id_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_roles": {"name": "user_roles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "bigint", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_role_user_fkey": {"name": "user_role_user_fkey", "tableFrom": "user_roles", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_role_role_fkey": {"name": "user_role_role_fkey", "tableFrom": "user_roles", "tableTo": "roles", "schemaTo": "public", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "definition": {"name": "definition", "type": "json", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"roles_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "roles_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.document_users": {"name": "document_users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "document_id": {"name": "document_id", "type": "bigint", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {"document_users_document_id_user_id_index": {"name": "document_users_document_id_user_id_index", "columns": [{"expression": "user_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}, {"expression": "document_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"documents_users_user_id_fkey": {"name": "documents_users_user_id_fkey", "tableFrom": "document_users", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "documents_users_document_id_fkey": {"name": "documents_users_document_id_fkey", "tableFrom": "document_users", "tableTo": "documents", "schemaTo": "public", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "owner_user_id": {"name": "owner_user_id", "type": "bigint", "primaryKey": false, "notNull": true}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true}, "extracted_path": {"name": "extracted_path", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status_message": {"name": "status_message", "type": "json", "primaryKey": false, "notNull": false}, "date_created": {"name": "date_created", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_modified": {"name": "date_modified", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_deleted": {"name": "date_deleted", "type": "timestamp", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "bigint", "primaryKey": false, "notNull": false}, "last_read_at": {"name": "last_read_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "page_count": {"name": "page_count", "type": "integer", "primaryKey": false, "notNull": false}, "user_tracking_id": {"name": "user_tracking_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "indexed": {"name": "indexed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "extracted_title": {"name": "extracted_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "meta": {"name": "meta", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"document_hash_filesize_index": {"name": "document_hash_filesize_index", "columns": [{"expression": "hash", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}, {"expression": "filesize", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_last_read_at_index": {"name": "document_last_read_at_index", "columns": [{"expression": "last_read_at", "asc": true, "nulls": "last", "opclass": "timestamp_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documents_created_at_index": {"name": "documents_created_at_index", "columns": [{"expression": "date_created", "asc": true, "nulls": "last", "opclass": "timestamp_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"documents_user_id_fkey": {"name": "documents_user_id_fkey", "tableFrom": "documents", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["owner_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"documents_uuid_key": {"columns": ["uuid"], "nullsNotDistinct": false, "name": "documents_uuid_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.summarisations": {"name": "summarisations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "document_id": {"name": "document_id", "type": "bigint", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": false}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "form_fields": {"name": "form_fields", "type": "text", "primaryKey": false, "notNull": true}, "meta": {"name": "meta", "type": "text", "primaryKey": false, "notNull": false}, "status_message": {"name": "status_message", "type": "json", "primaryKey": false, "notNull": false}, "date_created": {"name": "date_created", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_modified": {"name": "date_modified", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_deleted": {"name": "date_deleted", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"summarisations_documents_id_fkey": {"name": "summarisations_documents_id_fkey", "tableFrom": "summarisations", "tableTo": "documents", "schemaTo": "public", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"summarisations_uuid_key": {"columns": ["uuid"], "nullsNotDistinct": false, "name": "summarisations_uuid_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.beta_emails": {"name": "beta_emails", "schema": "", "columns": {"email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "added_by": {"name": "added_by", "type": "bigint", "primaryKey": false, "notNull": true}, "deleted_by": {"name": "deleted_by", "type": "bigint", "primaryKey": false, "notNull": false}, "claimed_by": {"name": "claimed_by", "type": "bigint", "primaryKey": false, "notNull": false}, "date_created": {"name": "date_created", "type": "timestamp", "primaryKey": false, "notNull": true}, "date_deleted": {"name": "date_deleted", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.document_embeddings": {"name": "document_embeddings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "bigint", "primaryKey": false, "notNull": true}, "document_id": {"name": "document_id", "type": "bigint", "primaryKey": false, "notNull": true}, "snippet": {"name": "snippet", "type": "text", "primaryKey": false, "notNull": false}, "embed": {"name": "embed", "type": "vector(1536)", "primaryKey": false, "notNull": false}}, "indexes": {"document_embeddings_user_document_id_index": {"name": "document_embeddings_user_document_id_index", "columns": [{"expression": "user_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}, {"expression": "document_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vector_user_id_fkey": {"name": "vector_user_id_fkey", "tableFrom": "document_embeddings", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vector_document_id_fkey": {"name": "vector_document_id_fkey", "tableFrom": "document_embeddings", "tableTo": "documents", "schemaTo": "public", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_settings": {"name": "user_settings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "bigint", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "str_value": {"name": "str_value", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "NULL"}, "int_value": {"name": "int_value", "type": "integer", "primaryKey": false, "notNull": false}, "bool_value": {"name": "bool_value", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"user_settings_user_id_index": {"name": "user_settings_user_id_index", "columns": [{"expression": "user_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_settings_user_id_key_index": {"name": "user_settings_user_id_key_index", "columns": [{"expression": "user_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}, {"expression": "key", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tts_voices": {"name": "tts_voices", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "external_id": {"name": "external_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "external_provider": {"name": "external_provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "date_created": {"name": "date_created", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_modified": {"name": "date_modified", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_deleted": {"name": "date_deleted", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tts_voices_uuid_key": {"columns": ["uuid"], "nullsNotDistinct": false, "name": "tts_voices_uuid_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.legacy_document_audio": {"name": "legacy_document_audio", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "document_id": {"name": "document_id", "type": "bigint", "primaryKey": false, "notNull": true}, "section_id": {"name": "section_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "paragraph_id": {"name": "paragraph_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "owner_user_id": {"name": "owner_user_id", "type": "bigint", "primaryKey": false, "notNull": true}, "external_task_id": {"name": "external_task_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tts_voice_id": {"name": "tts_voice_id", "type": "bigint", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "date_created": {"name": "date_created", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_modified": {"name": "date_modified", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_deleted": {"name": "date_deleted", "type": "timestamp", "primaryKey": false, "notNull": false}, "file_metadata": {"name": "file_metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"document_audio_document_id": {"name": "document_audio_document_id", "columns": [{"expression": "document_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_audio_external_task_id": {"name": "document_audio_external_task_id", "columns": [{"expression": "external_task_id", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_audio_owner_user_id": {"name": "document_audio_owner_user_id", "columns": [{"expression": "owner_user_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_audio_paragraph_id": {"name": "document_audio_paragraph_id", "columns": [{"expression": "paragraph_id", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_audio_section_id": {"name": "document_audio_section_id", "columns": [{"expression": "section_id", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"document_audio_document_id_fkey": {"name": "document_audio_document_id_fkey", "tableFrom": "legacy_document_audio", "tableTo": "documents", "schemaTo": "public", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "document_audio_user_id_fkey": {"name": "document_audio_user_id_fkey", "tableFrom": "legacy_document_audio", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["owner_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "document_audio_tts_voice_id_fkey": {"name": "document_audio_tts_voice_id_fkey", "tableFrom": "legacy_document_audio", "tableTo": "tts_voices", "schemaTo": "public", "columnsFrom": ["tts_voice_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"document_audio_uuid_key": {"columns": ["uuid"], "nullsNotDistinct": false, "name": "document_audio_uuid_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.section_summary": {"name": "section_summary", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "bigint", "primaryKey": false, "notNull": true}, "document_id": {"name": "document_id", "type": "bigint", "primaryKey": false, "notNull": true}, "section_id": {"name": "section_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "summary_response": {"name": "summary_response", "type": "text[]", "primaryKey": false, "notNull": false}, "date_created": {"name": "date_created", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_modified": {"name": "date_modified", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_deleted": {"name": "date_deleted", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"section_summary_uuid_index": {"name": "section_summary_uuid_index", "columns": [{"expression": "uuid", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"section_summary_document_id_fkey": {"name": "section_summary_document_id_fkey", "tableFrom": "section_summary", "tableTo": "documents", "schemaTo": "public", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "section_summary_user_id_fkey": {"name": "section_summary_user_id_fkey", "tableFrom": "section_summary", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"section_summary_uuid_key": {"columns": ["uuid"], "nullsNotDistinct": false, "name": "section_summary_uuid_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.document_audio": {"name": "document_audio", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "document_id": {"name": "document_id", "type": "bigint", "primaryKey": false, "notNull": true}, "block_id": {"name": "block_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "owner_user_id": {"name": "owner_user_id", "type": "bigint", "primaryKey": false, "notNull": true}, "voice_id": {"name": "voice_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "bigint", "primaryKey": false, "notNull": false}, "date_created": {"name": "date_created", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_modified": {"name": "date_modified", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_deleted": {"name": "date_deleted", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"doc_audio_document_id": {"name": "doc_audio_document_id", "columns": [{"expression": "document_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doc_audio_owner_user_id": {"name": "doc_audio_owner_user_id", "columns": [{"expression": "owner_user_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"doc_audio_document_id_fkey": {"name": "doc_audio_document_id_fkey", "tableFrom": "document_audio", "tableTo": "documents", "schemaTo": "public", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "doc_audio_user_id_fkey": {"name": "doc_audio_user_id_fkey", "tableFrom": "document_audio", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["owner_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"document_audio_uuid_key1": {"columns": ["uuid"], "nullsNotDistinct": false, "name": "document_audio_uuid_key1"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.document_highlights": {"name": "document_highlights", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "document_id": {"name": "document_id", "type": "bigint", "primaryKey": false, "notNull": true}, "owner_user_id": {"name": "owner_user_id", "type": "bigint", "primaryKey": false, "notNull": true}, "meta": {"name": "meta", "type": "json", "primaryKey": false, "notNull": true}, "date_created": {"name": "date_created", "type": "timestamp", "primaryKey": false, "notNull": false}, "date_modified": {"name": "date_modified", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"document_highlights_document_id": {"name": "document_highlights_document_id", "columns": [{"expression": "document_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_highlights_owner_user_id": {"name": "document_highlights_owner_user_id", "columns": [{"expression": "owner_user_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"document_highlights_document_id_fkey": {"name": "document_highlights_document_id_fkey", "tableFrom": "document_highlights", "tableTo": "documents", "schemaTo": "public", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "document_highlights_user_id_fkey": {"name": "document_highlights_user_id_fkey", "tableFrom": "document_highlights", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["owner_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"document_highlights_uuid_key": {"columns": ["uuid"], "nullsNotDistinct": false, "name": "document_highlights_uuid_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.comment_threads": {"name": "comment_threads", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "threadable_id": {"name": "threadable_id", "type": "integer", "primaryKey": false, "notNull": true}, "threadable_type": {"name": "threadable_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "meta": {"name": "meta", "type": "json", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"comment_threads_threadable_id_index": {"name": "comment_threads_threadable_id_index", "columns": [{"expression": "threadable_id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "comment_threads_uuid_index": {"name": "comment_threads_uuid_index", "columns": [{"expression": "uuid", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"comment_threads_uuid_key": {"columns": ["uuid"], "nullsNotDistinct": false, "name": "comment_threads_uuid_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.comments": {"name": "comments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "thread_id": {"name": "thread_id", "type": "integer", "primaryKey": false, "notNull": true}, "owner_user_id": {"name": "owner_user_id", "type": "integer", "primaryKey": false, "notNull": true}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"comments_owner_user_id_index": {"name": "comments_owner_user_id_index", "columns": [{"expression": "owner_user_id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "comments_thread_id_index": {"name": "comments_thread_id_index", "columns": [{"expression": "thread_id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "comments_uuid_index": {"name": "comments_uuid_index", "columns": [{"expression": "uuid", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"comments_thread_id_fkey": {"name": "comments_thread_id_fkey", "tableFrom": "comments", "tableTo": "comment_threads", "schemaTo": "public", "columnsFrom": ["thread_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"comments_uuid_key": {"columns": ["uuid"], "nullsNotDistinct": false, "name": "comments_uuid_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tags": {"name": "tags", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "owner_user_id": {"name": "owner_user_id", "type": "bigint", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"tags_name_index": {"name": "tags_name_index", "columns": [{"expression": "name", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "tags_uuid_index": {"name": "tags_uuid_index", "columns": [{"expression": "uuid", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tags_user_id_foreign_key": {"name": "tags_user_id_foreign_key", "tableFrom": "tags", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["owner_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tags_uuid_key": {"columns": ["uuid"], "nullsNotDistinct": false, "name": "tags_uuid_key"}, "unique_user_tag_name": {"columns": ["name", "owner_user_id"], "nullsNotDistinct": false, "name": "unique_user_tag_name"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.taggables": {"name": "taggables", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tag_id": {"name": "tag_id", "type": "bigint", "primaryKey": false, "notNull": true}, "taggable_type": {"name": "taggable_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "taggable_id": {"name": "taggable_id", "type": "bigint", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"taggables_taggable_types_index": {"name": "taggables_taggable_types_index", "columns": [{"expression": "taggable_type", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}, {"expression": "taggable_id", "asc": true, "nulls": "last", "opclass": "int8_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"taggables_tag_id_foreign_key": {"name": "taggables_tag_id_foreign_key", "tableFrom": "taggables", "tableTo": "tags", "schemaTo": "public", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_taggable_item": {"columns": ["tag_id", "taggable_type", "taggable_id"], "nullsNotDistinct": false, "name": "unique_taggable_item"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {"users": {"columns": {"role": {"isDefaultAnExpression": true}}}, "user_settings": {"columns": {"str_value": {"isDefaultAnExpression": true}}}, "section_summary": {"columns": {"summary_response": {"isArray": true, "dimensions": 1, "rawType": "text"}}}}}}