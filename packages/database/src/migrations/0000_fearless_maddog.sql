-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
-- CREATE TABLE "kysely_migration" (
-- 	"name" varchar(255) PRIMARY KEY NOT NULL,
-- 	"timestamp" varchar(255) NOT NULL
-- );
-- --> statement-breakpoint
-- CREATE TABLE "kysely_migration_lock" (
-- 	"id" varchar(255) PRIMARY KEY NOT NULL,
-- 	"is_locked" integer DEFAULT 0 NOT NULL
-- );
-- --> statement-breakpoint
-- CREATE TABLE "users" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"estendio_id" varchar(255),
-- 	"role" varchar(255) DEFAULT NULL,
-- 	"created_at" timestamp NOT NULL,
-- 	"modified_at" timestamp,
-- 	"deleted_at" timestamp,
-- 	CONSTRAINT "users_estendio_id_key" UNIQUE("estendio_id")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "user_roles" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"user_id" bigint NOT NULL,
-- 	"role_id" bigint NOT NULL
-- );
-- --> statement-breakpoint
-- CREATE TABLE "roles" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"name" varchar(255) NOT NULL,
-- 	"definition" json NOT NULL,
-- 	CONSTRAINT "roles_name_key" UNIQUE("name")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "document_users" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"document_id" bigint NOT NULL,
-- 	"user_id" bigint NOT NULL
-- );
-- --> statement-breakpoint
-- CREATE TABLE "documents" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"uuid" varchar(255) NOT NULL,
-- 	"owner_user_id" bigint NOT NULL,
-- 	"filename" varchar(255) NOT NULL,
-- 	"type" varchar(255) NOT NULL,
-- 	"path" text NOT NULL,
-- 	"extracted_path" text,
-- 	"status" varchar(255) NOT NULL,
-- 	"status_message" json,
-- 	"date_created" timestamp,
-- 	"date_modified" timestamp,
-- 	"date_deleted" timestamp,
-- 	"hash" varchar(255),
-- 	"filesize" bigint,
-- 	"last_read_at" timestamp,
-- 	"page_count" integer,
-- 	"user_tracking_id" varchar(255),
-- 	"indexed" boolean DEFAULT false,
-- 	"extracted_title" varchar(255),
-- 	"display_name" varchar(255),
-- 	"meta" json,
-- 	CONSTRAINT "documents_uuid_key" UNIQUE("uuid")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "summarisations" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"uuid" varchar(255) NOT NULL,
-- 	"name" varchar(255) NOT NULL,
-- 	"document_id" bigint NOT NULL,
-- 	"type" varchar(255),
-- 	"path" text,
-- 	"prompt" text NOT NULL,
-- 	"status" varchar(255) NOT NULL,
-- 	"form_fields" text NOT NULL,
-- 	"meta" text,
-- 	"status_message" json,
-- 	"date_created" timestamp,
-- 	"date_modified" timestamp,
-- 	"date_deleted" timestamp,
-- 	CONSTRAINT "summarisations_uuid_key" UNIQUE("uuid")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "beta_emails" (
-- 	"email" varchar(255) PRIMARY KEY NOT NULL,
-- 	"added_by" bigint NOT NULL,
-- 	"deleted_by" bigint,
-- 	"claimed_by" bigint,
-- 	"date_created" timestamp NOT NULL,
-- 	"date_deleted" timestamp
-- );
-- --> statement-breakpoint
-- CREATE TABLE "document_embeddings" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"user_id" bigint NOT NULL,
-- 	"document_id" bigint NOT NULL,
-- 	"snippet" text,
-- 	"embed" vector(1536)
-- );
-- --> statement-breakpoint
-- CREATE TABLE "user_settings" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"user_id" bigint NOT NULL,
-- 	"key" varchar NOT NULL,
-- 	"str_value" varchar(255) DEFAULT NULL,
-- 	"int_value" integer,
-- 	"bool_value" boolean
-- );
-- --> statement-breakpoint
-- CREATE TABLE "tts_voices" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"uuid" varchar(255) NOT NULL,
-- 	"name" varchar(255) NOT NULL,
-- 	"external_id" varchar(255) NOT NULL,
-- 	"external_provider" varchar(255) NOT NULL,
-- 	"date_created" timestamp,
-- 	"date_modified" timestamp,
-- 	"date_deleted" timestamp,
-- 	CONSTRAINT "tts_voices_uuid_key" UNIQUE("uuid")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "legacy_document_audio" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"uuid" varchar(255) NOT NULL,
-- 	"document_id" bigint NOT NULL,
-- 	"section_id" varchar(255) NOT NULL,
-- 	"paragraph_id" varchar(255) NOT NULL,
-- 	"owner_user_id" bigint NOT NULL,
-- 	"external_task_id" varchar(255) NOT NULL,
-- 	"tts_voice_id" bigint NOT NULL,
-- 	"name" varchar(255),
-- 	"type" varchar(255),
-- 	"path" text,
-- 	"status" varchar(255) NOT NULL,
-- 	"date_created" timestamp,
-- 	"date_modified" timestamp,
-- 	"date_deleted" timestamp,
-- 	"file_metadata" json,
-- 	CONSTRAINT "document_audio_uuid_key" UNIQUE("uuid")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "section_summary" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"uuid" varchar(255) NOT NULL,
-- 	"user_id" bigint NOT NULL,
-- 	"document_id" bigint NOT NULL,
-- 	"section_id" varchar(255) NOT NULL,
-- 	"summary_response" text[],
-- 	"date_created" timestamp,
-- 	"date_modified" timestamp,
-- 	"date_deleted" timestamp,
-- 	CONSTRAINT "section_summary_uuid_key" UNIQUE("uuid")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "document_audio" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"uuid" varchar(255) NOT NULL,
-- 	"document_id" bigint NOT NULL,
-- 	"block_id" varchar(255) NOT NULL,
-- 	"owner_user_id" bigint NOT NULL,
-- 	"voice_id" varchar(255) NOT NULL,
-- 	"path" text,
-- 	"duration" bigint,
-- 	"date_created" timestamp,
-- 	"date_modified" timestamp,
-- 	"date_deleted" timestamp,
-- 	CONSTRAINT "document_audio_uuid_key1" UNIQUE("uuid")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "document_highlights" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"uuid" varchar(255) NOT NULL,
-- 	"document_id" bigint NOT NULL,
-- 	"owner_user_id" bigint NOT NULL,
-- 	"meta" json NOT NULL,
-- 	"date_created" timestamp,
-- 	"date_modified" timestamp,
-- 	CONSTRAINT "document_highlights_uuid_key" UNIQUE("uuid")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "comment_threads" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"uuid" varchar(255) NOT NULL,
-- 	"threadable_id" integer NOT NULL,
-- 	"threadable_type" varchar(255) NOT NULL,
-- 	"meta" json NOT NULL,
-- 	"created_at" timestamp DEFAULT now() NOT NULL,
-- 	"updated_at" timestamp DEFAULT now() NOT NULL,
-- 	"deleted_at" timestamp,
-- 	CONSTRAINT "comment_threads_uuid_key" UNIQUE("uuid")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "comments" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"uuid" varchar(255) NOT NULL,
-- 	"thread_id" integer NOT NULL,
-- 	"owner_user_id" integer NOT NULL,
-- 	"text" text NOT NULL,
-- 	"created_at" timestamp DEFAULT now() NOT NULL,
-- 	"updated_at" timestamp DEFAULT now() NOT NULL,
-- 	"deleted_at" timestamp,
-- 	CONSTRAINT "comments_uuid_key" UNIQUE("uuid")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "tags" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"uuid" uuid DEFAULT gen_random_uuid() NOT NULL,
-- 	"name" varchar(32) NOT NULL,
-- 	"owner_user_id" bigint NOT NULL,
-- 	"created_at" timestamp DEFAULT now() NOT NULL,
-- 	CONSTRAINT "tags_uuid_key" UNIQUE("uuid"),
-- 	CONSTRAINT "unique_user_tag_name" UNIQUE("name","owner_user_id")
-- );
-- --> statement-breakpoint
-- CREATE TABLE "taggables" (
-- 	"id" serial PRIMARY KEY NOT NULL,
-- 	"tag_id" bigint NOT NULL,
-- 	"taggable_type" varchar(255) NOT NULL,
-- 	"taggable_id" bigint NOT NULL,
-- 	"created_at" timestamp DEFAULT now() NOT NULL,
-- 	CONSTRAINT "unique_taggable_item" UNIQUE("tag_id","taggable_type","taggable_id")
-- );
-- --> statement-breakpoint
-- ALTER TABLE "user_roles" ADD CONSTRAINT "user_role_user_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "user_roles" ADD CONSTRAINT "user_role_role_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "document_users" ADD CONSTRAINT "documents_users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "document_users" ADD CONSTRAINT "documents_users_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "public"."documents"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "documents" ADD CONSTRAINT "documents_user_id_fkey" FOREIGN KEY ("owner_user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "summarisations" ADD CONSTRAINT "summarisations_documents_id_fkey" FOREIGN KEY ("document_id") REFERENCES "public"."documents"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "document_embeddings" ADD CONSTRAINT "vector_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "document_embeddings" ADD CONSTRAINT "vector_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "public"."documents"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "legacy_document_audio" ADD CONSTRAINT "document_audio_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "public"."documents"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "legacy_document_audio" ADD CONSTRAINT "document_audio_user_id_fkey" FOREIGN KEY ("owner_user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "legacy_document_audio" ADD CONSTRAINT "document_audio_tts_voice_id_fkey" FOREIGN KEY ("tts_voice_id") REFERENCES "public"."tts_voices"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "section_summary" ADD CONSTRAINT "section_summary_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "public"."documents"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "section_summary" ADD CONSTRAINT "section_summary_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "document_audio" ADD CONSTRAINT "doc_audio_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "public"."documents"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "document_audio" ADD CONSTRAINT "doc_audio_user_id_fkey" FOREIGN KEY ("owner_user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "document_highlights" ADD CONSTRAINT "document_highlights_document_id_fkey" FOREIGN KEY ("document_id") REFERENCES "public"."documents"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "document_highlights" ADD CONSTRAINT "document_highlights_user_id_fkey" FOREIGN KEY ("owner_user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "comments" ADD CONSTRAINT "comments_thread_id_fkey" FOREIGN KEY ("thread_id") REFERENCES "public"."comment_threads"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "tags" ADD CONSTRAINT "tags_user_id_foreign_key" FOREIGN KEY ("owner_user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "taggables" ADD CONSTRAINT "taggables_tag_id_foreign_key" FOREIGN KEY ("tag_id") REFERENCES "public"."tags"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- CREATE INDEX "user_estendio_id_index" ON "users" USING btree ("estendio_id" text_ops);--> statement-breakpoint
-- CREATE INDEX "document_users_document_id_user_id_index" ON "document_users" USING btree ("user_id" int8_ops,"document_id" int8_ops);--> statement-breakpoint
-- CREATE INDEX "document_hash_filesize_index" ON "documents" USING btree ("hash" int8_ops,"filesize" text_ops);--> statement-breakpoint
-- CREATE INDEX "document_last_read_at_index" ON "documents" USING btree ("last_read_at" timestamp_ops);--> statement-breakpoint
-- CREATE INDEX "documents_created_at_index" ON "documents" USING btree ("date_created" timestamp_ops);--> statement-breakpoint
-- CREATE INDEX "document_embeddings_user_document_id_index" ON "document_embeddings" USING btree ("user_id" int8_ops,"document_id" int8_ops);--> statement-breakpoint
-- CREATE INDEX "user_settings_user_id_index" ON "user_settings" USING btree ("user_id" int8_ops);--> statement-breakpoint
-- CREATE INDEX "user_settings_user_id_key_index" ON "user_settings" USING btree ("user_id" int8_ops,"key" int8_ops);--> statement-breakpoint
-- CREATE INDEX "document_audio_document_id" ON "legacy_document_audio" USING btree ("document_id" int8_ops);--> statement-breakpoint
-- CREATE INDEX "document_audio_external_task_id" ON "legacy_document_audio" USING btree ("external_task_id" text_ops);--> statement-breakpoint
-- CREATE INDEX "document_audio_owner_user_id" ON "legacy_document_audio" USING btree ("owner_user_id" int8_ops);--> statement-breakpoint
-- CREATE INDEX "document_audio_paragraph_id" ON "legacy_document_audio" USING btree ("paragraph_id" text_ops);--> statement-breakpoint
-- CREATE INDEX "document_audio_section_id" ON "legacy_document_audio" USING btree ("section_id" text_ops);--> statement-breakpoint
-- CREATE INDEX "section_summary_uuid_index" ON "section_summary" USING btree ("uuid" text_ops);--> statement-breakpoint
-- CREATE INDEX "doc_audio_document_id" ON "document_audio" USING btree ("document_id" int8_ops);--> statement-breakpoint
-- CREATE INDEX "doc_audio_owner_user_id" ON "document_audio" USING btree ("owner_user_id" int8_ops);--> statement-breakpoint
-- CREATE INDEX "document_highlights_document_id" ON "document_highlights" USING btree ("document_id" int8_ops);--> statement-breakpoint
-- CREATE INDEX "document_highlights_owner_user_id" ON "document_highlights" USING btree ("owner_user_id" int8_ops);--> statement-breakpoint
-- CREATE INDEX "comment_threads_threadable_id_index" ON "comment_threads" USING btree ("threadable_id" int4_ops);--> statement-breakpoint
-- CREATE INDEX "comment_threads_uuid_index" ON "comment_threads" USING btree ("uuid" text_ops);--> statement-breakpoint
-- CREATE INDEX "comments_owner_user_id_index" ON "comments" USING btree ("owner_user_id" int4_ops);--> statement-breakpoint
-- CREATE INDEX "comments_thread_id_index" ON "comments" USING btree ("thread_id" int4_ops);--> statement-breakpoint
-- CREATE INDEX "comments_uuid_index" ON "comments" USING btree ("uuid" text_ops);--> statement-breakpoint
-- CREATE INDEX "tags_name_index" ON "tags" USING btree ("name" text_ops);--> statement-breakpoint
-- CREATE INDEX "tags_uuid_index" ON "tags" USING btree ("uuid" uuid_ops);--> statement-breakpoint
-- CREATE INDEX "taggables_taggable_types_index" ON "taggables" USING btree ("taggable_type" int8_ops,"taggable_id" int8_ops);