import { Pool, PoolConfig, Client, ClientConfig } from "pg";
import * as dotenv from "dotenv";
import * as schema from "./schema"; // Your combined schema
import {
  NodePgDatabase,
  drizzle as nodeDrizzle,
} from "drizzle-orm/node-postgres";
import type { AwsDataApiPgDatabase } from "drizzle-orm/aws-data-api/pg";
import { RDSDataClient } from "@aws-sdk/client-rds-data";
dotenv.config({ path: "../../../.env" });

// --- Configuration Types ---
interface NodePgConfig {
  type: "node-postgres";
  poolConfig: PoolConfig;
}

interface AwsDataApiPgConfig {
  type: "aws-data-api-pg";
  awsRdsDataClientConfig: {
    region: string;
  };
  resourceArn: string;
  secretArn: string;
  databaseName: string;
}

type DbDriverConfig = NodePgConfig | AwsDataApiPgConfig;

function getDbDriverConfig(): DbDriverConfig {
  const connectionType =
    process.env.DB_CONNECTION_TYPE?.toLowerCase() || "local";
  console.log(
    `Attempting to use database configuration type: ${connectionType}`
  );

  switch (connectionType) {
    case "local": // Local connection via node-postgres
      if (
        !process.env.LOCAL_DATABASE_HOST ||
        !process.env.LOCAL_DATABASE_PORT ||
        !process.env.LOCAL_DATABASE_USER ||
        !process.env.LOCAL_DATABASE_NAME
      ) {
        console.log(process.env);
        throw new Error(
          "Missing one or more local environment variables (LOCAL_DATABASE_HOST, LOCAL_DATABASE_PORT, LOCAL_DATABASE_USER, LOCAL_DATABASE_NAME)."
        );
      }
      return {
        type: "node-postgres",
        poolConfig: {
          host: process.env.LOCAL_DATABASE_HOST || "127.0.0.1",
          port: Number(process.env.LOCAL_DATABASE_PORT) || 5432,
          user: process.env.LOCAL_DATABASE_USER || "admin",
          database: process.env.LOCAL_DATABASE_NAME || "postgres",
        },
      };

    case "aws-data-api-pg":
      if (
        !process.env.RDS_AWS_REGION ||
        !process.env.RDS_RESOURCE_ARN ||
        !process.env.RDS_SECRET_ARN ||
        !process.env.RDS_DATABASE_NAME
      ) {
        throw new Error(
          "Missing one or more AWS Data API environment variables (AWS_REGION, RDS_RESOURCE_ARN, RDS_SECRET_ARN, RDS_DATABASE_NAME)."
        );
      }
      return {
        type: "aws-data-api-pg",
        awsRdsDataClientConfig: {
          region: process.env.RDS_AWS_REGION,
        },
        resourceArn: process.env.RDS_RESOURCE_ARN,
        secretArn: process.env.RDS_SECRET_ARN,
        databaseName: process.env.RDS_DATABASE_NAME,
      };

    default:
      throw new Error(`Unsupported DB_CONNECTION_TYPE: ${connectionType}`);
  }
}

type DrizzleDb =
  | NodePgDatabase<typeof schema>
  | AwsDataApiPgDatabase<typeof schema>;

interface DbClient {
  db: DrizzleDb;
  pool?: Pool;
  dataApiClient?: RDSDataClient;
  dialect: DbDriverConfig["type"];
}

async function createDbClient(driverConfig: DbDriverConfig): Promise<DbClient> {
  if (driverConfig.type === "node-postgres") {
    // Load driver in now as we don't want to do it unless we need it
    const { drizzle: drizzleNodePg } = await import(
      "drizzle-orm/node-postgres"
    );
    const pool = new Pool(driverConfig.poolConfig);

    const db = drizzleNodePg(pool, {
      schema,
      logger: process.env.DRIZZLE_LOGGER === "true",
    });

    return { db: db as DrizzleDb, pool, dialect: "node-postgres" };
  } else if (driverConfig.type === "aws-data-api-pg") {
    // Load driver in now as we don't want to do it unless we need it
    const { drizzle: drizzleAwsDataApi } = await import(
      "drizzle-orm/aws-data-api/pg"
    );
    const client = new RDSDataClient(driverConfig.awsRdsDataClientConfig);

    const db = drizzleAwsDataApi(client, {
      schema,
      resourceArn: driverConfig.resourceArn,
      secretArn: driverConfig.secretArn,
      database: driverConfig.databaseName, // 'database' is the option name for drizzleAwsDataApi
      logger: process.env.DRIZZLE_LOGGER === "true",
    });

    return {
      db: db as DrizzleDb,
      dataApiClient: client,
      dialect: "aws-data-api-pg",
    };
  }
  // Should be unreachable due to getDbDriverConfig throwing an error for unsupported types
  throw new Error(
    "Internal error: Unhandled configuration type in createDbClient."
  );
}

export const dbClient: Promise<DbClient> = (async () => {
  const activeDbConfig = getDbDriverConfig();
  return createDbClient(activeDbConfig);
})();

export async function testDbConnection() {
  console.log("Attempting to test database connection...");
  try {
    const { db, dialect } = await dbClient;
    await (db as any).execute("SELECT 1 as result;");
    console.log(`Database connection successful using dialect: ${dialect}!`);
  } catch (error) {
    console.error("Failed to connect to or query the database:", error);
    throw error;
  }
}
