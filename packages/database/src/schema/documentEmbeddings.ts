import {
  pgTable,
  index,
  serial,
  foreignKey,
  bigint,
  text,
  vector,
} from "drizzle-orm/pg-core";
import { users } from "./users";
import { documents } from "./documents";
import { relations } from "drizzle-orm";

export const documentEmbeddings = pgTable(
  "document_embeddings",
  {
    id: serial().primaryKey().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    userId: bigint("user_id", { mode: "number" }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    documentId: bigint("document_id", { mode: "number" }).notNull(),
    snippet: text(),
    embed: vector({ dimensions: 1536 }),
  },
  (table) => [
    index("document_embeddings_user_document_id_index").using(
      "btree",
      table.userId.asc().nullsLast().op("int8_ops"),
      table.documentId.asc().nullsLast().op("int8_ops")
    ),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.id],
      name: "vector_user_id_fkey",
    }).onDelete("cascade"),
    foreignKey({
      columns: [table.documentId],
      foreignColumns: [documents.id],
      name: "vector_document_id_fkey",
    }).onDelete("cascade"),
  ]
);

export const documentEmbeddingsRelations = relations(
  documentEmbeddings,
  ({ one }) => ({
    user: one(users, {
      fields: [documentEmbeddings.userId],
      references: [users.id],
    }),
    document: one(documents, {
      fields: [documentEmbeddings.documentId],
      references: [documents.id],
    }),
  })
);
