import {
  pgTable,
  varchar,
  integer,
  index,
  unique,
  serial,
  timestamp,
  foreignKey,
  bigint,
  json,
  text,
  boolean,
  vector,
  uuid,
} from "drizzle-orm/pg-core";
import { relations, sql } from "drizzle-orm";
import { comments } from "./comments";

export const commentThreads = pgTable(
  "comment_threads",
  {
    id: serial().primaryKey().notNull(),
    uuid: varchar({ length: 255 }).notNull(),
    threadableId: integer("threadable_id").notNull(),
    threadableType: varchar("threadable_type", { length: 255 }).notNull(),
    meta: json().notNull(),
    createdAt: timestamp("created_at", { mode: "string" })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp("updated_at", { mode: "string" })
      .defaultNow()
      .notNull(),
    deletedAt: timestamp("deleted_at", { mode: "string" }),
  },
  (table) => [
    index().using("btree", table.threadableId.asc().nullsLast().op("int4_ops")),
    index().using("btree", table.uuid.asc().nullsLast().op("text_ops")),
    unique("comment_threads_uuid_key").on(table.uuid),
  ]
);

export const commentThreadsRelations = relations(
  commentThreads,
  ({ many }) => ({
    comments: many(comments),
  })
);
