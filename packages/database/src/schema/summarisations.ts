import {
  pgTable,
  varchar,
  unique,
  serial,
  timestamp,
  foreignKey,
  bigint,
  json,
  text,
} from "drizzle-orm/pg-core";
import { documents } from "./documents";
import { relations } from "drizzle-orm";

export const summarisations = pgTable(
  "summarisations",
  {
    id: serial().primaryKey().notNull(),
    uuid: varchar({ length: 255 }).notNull(),
    name: varchar({ length: 255 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    documentId: bigint("document_id", { mode: "number" }).notNull(),
    type: varchar({ length: 255 }),
    path: text(),
    prompt: text().notNull(),
    status: varchar({ length: 255 }).notNull(),
    formFields: text("form_fields").notNull(),
    meta: text(),
    statusMessage: json("status_message"),
    dateCreated: timestamp("date_created", { mode: "string" }),
    dateModified: timestamp("date_modified", { mode: "string" }),
    dateDeleted: timestamp("date_deleted", { mode: "string" }),
  },
  (table) => [
    foreignKey({
      columns: [table.documentId],
      foreignColumns: [documents.id],
      name: "summarisations_documents_id_fkey",
    }).onDelete("cascade"),
    unique("summarisations_uuid_key").on(table.uuid),
  ]
);

export const summarisationsRelations = relations(summarisations, ({ one }) => ({
  document: one(documents, {
    fields: [summarisations.documentId],
    references: [documents.id],
  }),
}));
