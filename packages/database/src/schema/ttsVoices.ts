import {
  pgTable,
  varchar,
  unique,
  serial,
  timestamp,
} from "drizzle-orm/pg-core";
import { legacyDocumentAudio } from "./legacyDocumentAudio";
import { relations } from "drizzle-orm";

export const ttsVoices = pgTable(
  "tts_voices",
  {
    id: serial().primaryKey().notNull(),
    uuid: varchar({ length: 255 }).notNull(),
    name: varchar({ length: 255 }).notNull(),
    externalId: varchar("external_id", { length: 255 }).notNull(),
    externalProvider: varchar("external_provider", { length: 255 }).notNull(),
    dateCreated: timestamp("date_created", { mode: "string" }),
    dateModified: timestamp("date_modified", { mode: "string" }),
    dateDeleted: timestamp("date_deleted", { mode: "string" }),
  },
  (table) => [unique("tts_voices_uuid_key").on(table.uuid)]
);

export const ttsVoicesRelations = relations(ttsVoices, ({ many }) => ({
  legacyDocumentAudios: many(legacyDocumentAudio),
}));
