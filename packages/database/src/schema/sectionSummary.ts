import {
  pgTable,
  varchar,
  index,
  unique,
  serial,
  timestamp,
  foreignKey,
  bigint,
  text,
} from "drizzle-orm/pg-core";
import { documents } from "./documents";
import { users } from "./users";
import { relations } from "drizzle-orm";

export const sectionSummary = pgTable(
  "section_summary",
  {
    id: serial().primaryKey().notNull(),
    uuid: varchar({ length: 255 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    userId: bigint("user_id", { mode: "number" }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    documentId: bigint("document_id", { mode: "number" }).notNull(),
    sectionId: varchar("section_id", { length: 255 }).notNull(),
    summaryResponse: text("summary_response").array(),
    dateCreated: timestamp("date_created", { mode: "string" }),
    dateModified: timestamp("date_modified", { mode: "string" }),
    dateDeleted: timestamp("date_deleted", { mode: "string" }),
  },
  (table) => [
    index().using("btree", table.uuid.asc().nullsLast().op("text_ops")),
    foreignKey({
      columns: [table.documentId],
      foreignColumns: [documents.id],
      name: "section_summary_document_id_fkey",
    }).onDelete("cascade"),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.id],
      name: "section_summary_user_id_fkey",
    }).onDelete("cascade"),
    unique("section_summary_uuid_key").on(table.uuid),
  ]
);

export const sectionSummaryRelations = relations(sectionSummary, ({ one }) => ({
  document: one(documents, {
    fields: [sectionSummary.documentId],
    references: [documents.id],
  }),
  user: one(users, {
    fields: [sectionSummary.userId],
    references: [users.id],
  }),
}));
