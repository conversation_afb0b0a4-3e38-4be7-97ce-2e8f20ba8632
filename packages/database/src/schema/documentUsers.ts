import {
  pgTable,
  index,
  serial,
  foreignKey,
  bigint,
} from "drizzle-orm/pg-core";
import { users } from "./users";
import { documents } from "./documents";
import { relations } from "drizzle-orm";

export const documentUsers = pgTable(
  "document_users",
  {
    id: serial().primaryKey().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    documentId: bigint("document_id", { mode: "number" }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    userId: bigint("user_id", { mode: "number" }).notNull(),
  },
  (table) => [
    index("document_users_document_id_user_id_index").using(
      "btree",
      table.userId.asc().nullsLast().op("int8_ops"),
      table.documentId.asc().nullsLast().op("int8_ops")
    ),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [users.id],
      name: "documents_users_user_id_fkey",
    }).onDelete("cascade"),
    foreignKey({
      columns: [table.documentId],
      foreignColumns: [documents.id],
      name: "documents_users_document_id_fkey",
    }).onDelete("cascade"),
  ]
);

export const documentUsersRelations = relations(documentUsers, ({ one }) => ({
  user: one(users, {
    fields: [documentUsers.userId],
    references: [users.id],
  }),
  document: one(documents, {
    fields: [documentUsers.documentId],
    references: [documents.id],
  }),
}));
