import {
  pgTable,
  varchar,
  integer,
  index,
  unique,
  serial,
  timestamp,
  foreignKey,
  bigint,
  json,
  text,
  boolean,
} from "drizzle-orm/pg-core";
import { users } from "./users";
import { relations } from "drizzle-orm";
import { documentAudio } from "./documentAudio";
import { documentEmbeddings } from "./documentEmbeddings";
import { documentHighlights } from "./documentHighlights";
import { documentUsers } from "./documentUsers";
import { legacyDocumentAudio } from "./legacyDocumentAudio";
import { sectionSummary } from "./sectionSummary";
import { summarisations } from "./summarisations";

export const documents = pgTable(
  "documents",
  {
    id: serial().primaryKey().notNull(),
    uuid: varchar({ length: 255 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    ownerUserId: bigint("owner_user_id", { mode: "number" }).notNull(),
    filename: varchar({ length: 255 }).notNull(),
    type: varchar({ length: 255 }).notNull(),
    path: text().notNull(),
    extractedPath: text("extracted_path"),
    status: varchar({ length: 255 }).notNull(),
    statusMessage: json("status_message"),
    dateCreated: timestamp("date_created", { mode: "string" }),
    dateModified: timestamp("date_modified", { mode: "string" }),
    dateDeleted: timestamp("date_deleted", { mode: "string" }),
    hash: varchar({ length: 255 }),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    filesize: bigint({ mode: "number" }),
    lastReadAt: timestamp("last_read_at", { mode: "string" }),
    pageCount: integer("page_count"),
    userTrackingId: varchar("user_tracking_id", { length: 255 }),
    indexed: boolean().default(false),
    extractedTitle: varchar("extracted_title", { length: 255 }),
    displayName: varchar("display_name", { length: 255 }),
    meta: json(),
  },
  (table) => [
    index("document_hash_filesize_index").using(
      "btree",
      table.hash.asc().nullsLast().op("text_ops"),
      table.filesize.asc().nullsLast().op("int8_ops")
    ),
    index("document_last_read_at_index").using(
      "btree",
      table.lastReadAt.asc().nullsLast().op("timestamp_ops")
    ),
    index("documents_created_at_index").using(
      "btree",
      table.dateCreated.asc().nullsLast().op("timestamp_ops")
    ),
    foreignKey({
      columns: [table.ownerUserId],
      foreignColumns: [users.id],
      name: "documents_user_id_fkey",
    }).onDelete("cascade"),
    unique("documents_uuid_key").on(table.uuid),
  ]
);

export const documentsRelations = relations(documents, ({ one, many }) => ({
  documentUsers: many(documentUsers),
  user: one(users, {
    fields: [documents.ownerUserId],
    references: [users.id],
  }),
  summarisations: many(summarisations),
  documentEmbeddings: many(documentEmbeddings),
  legacyDocumentAudios: many(legacyDocumentAudio),
  sectionSummaries: many(sectionSummary),
  documentAudios: many(documentAudio),
  documentHighlights: many(documentHighlights),
}));

export type Document = typeof documents.$inferSelect;
export type NewDocument = typeof documents.$inferInsert;
