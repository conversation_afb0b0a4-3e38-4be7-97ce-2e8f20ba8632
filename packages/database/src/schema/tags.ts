import {
  pgTable,
  varchar,
  index,
  unique,
  serial,
  timestamp,
  foreignKey,
  bigint,
  uuid,
} from "drizzle-orm/pg-core";
import { relations, sql } from "drizzle-orm";

import { users } from "./users";
import { taggables } from "./taggables";

export const tags = pgTable(
  "tags",
  {
    id: serial().primaryKey().notNull(),
    uuid: uuid().defaultRandom().notNull(),
    name: varchar({ length: 32 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    ownerUserId: bigint("owner_user_id", { mode: "number" }).notNull(),
    createdAt: timestamp("created_at", { mode: "string" })
      .defaultNow()
      .notNull(),
  },
  (table) => [
    index().using("btree", table.name.asc().nullsLast().op("text_ops")),
    index().using("btree", table.uuid.asc().nullsLast().op("uuid_ops")),
    foreignKey({
      columns: [table.ownerUserId],
      foreignColumns: [users.id],
      name: "tags_user_id_foreign_key",
    }).onDelete("cascade"),
    unique("tags_uuid_key").on(table.uuid),
    unique("unique_user_tag_name").on(table.name, table.ownerUserId),
  ]
);

export const tagsRelations = relations(tags, ({ one, many }) => ({
  user: one(users, {
    fields: [tags.ownerUserId],
    references: [users.id],
  }),
  taggables: many(taggables),
}));
