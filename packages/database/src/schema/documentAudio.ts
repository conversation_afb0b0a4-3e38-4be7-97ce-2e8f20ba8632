import {
  pgTable,
  varchar,
  index,
  unique,
  serial,
  timestamp,
  foreignKey,
  bigint,
  text,
} from "drizzle-orm/pg-core";
import { documents } from "./documents";
import { users } from "./users";
import { relations } from "drizzle-orm";

export const documentAudio = pgTable(
  "document_audio",
  {
    id: serial().primaryKey().notNull(),
    uuid: varchar({ length: 255 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    documentId: bigint("document_id", { mode: "number" }).notNull(),
    blockId: varchar("block_id", { length: 255 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    ownerUserId: bigint("owner_user_id", { mode: "number" }).notNull(),
    voiceId: varchar("voice_id", { length: 255 }).notNull(),
    path: text(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    duration: bigint({ mode: "number" }),
    dateCreated: timestamp("date_created", { mode: "string" }),
    dateModified: timestamp("date_modified", { mode: "string" }),
    dateDeleted: timestamp("date_deleted", { mode: "string" }),
  },
  (table) => [
    index("doc_audio_document_id").using(
      "btree",
      table.documentId.asc().nullsLast().op("int8_ops")
    ),
    index("doc_audio_owner_user_id").using(
      "btree",
      table.ownerUserId.asc().nullsLast().op("int8_ops")
    ),
    foreignKey({
      columns: [table.documentId],
      foreignColumns: [documents.id],
      name: "doc_audio_document_id_fkey",
    }).onDelete("cascade"),
    foreignKey({
      columns: [table.ownerUserId],
      foreignColumns: [users.id],
      name: "doc_audio_user_id_fkey",
    }).onDelete("cascade"),
    unique("document_audio_uuid_key1").on(table.uuid),
  ]
);

export const documentAudioRelations = relations(documentAudio, ({ one }) => ({
  document: one(documents, {
    fields: [documentAudio.documentId],
    references: [documents.id],
  }),
  user: one(users, {
    fields: [documentAudio.ownerUserId],
    references: [users.id],
  }),
}));
