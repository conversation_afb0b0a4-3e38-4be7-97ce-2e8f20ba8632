import {
  pgTable,
  varchar,
  integer,
  index,
  serial,
  bigint,
  boolean,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";

export const userSettings = pgTable(
  "user_settings",
  {
    id: serial().primaryKey().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    userId: bigint("user_id", { mode: "number" }).notNull(),
    key: varchar().notNull(),
    strValue: varchar("str_value", { length: 255 }).default(sql`NULL`),
    intValue: integer("int_value"),
    boolValue: boolean("bool_value"),
  },
  (table) => [
    index().using("btree", table.userId.asc().nullsLast().op("int8_ops")),
    index().using(
      "btree",
      table.userId.asc().nullsLast().op("int8_ops"),
      table.key.asc().nullsLast().op("text_ops")
    ),
  ]
);
