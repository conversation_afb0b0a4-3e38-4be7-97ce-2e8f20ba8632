import { relations } from "drizzle-orm";
import { pgTable, varchar, unique, serial, json } from "drizzle-orm/pg-core";
import { userRoles } from "./userRoles";

export const roles = pgTable(
  "roles",
  {
    id: serial().primaryKey().notNull(),
    name: varchar({ length: 255 }).notNull(),
    definition: json().notNull(),
  },
  (table) => [unique("roles_name_key").on(table.name)]
);

export const rolesRelations = relations(roles, ({ many }) => ({
  userRoles: many(userRoles),
}));
