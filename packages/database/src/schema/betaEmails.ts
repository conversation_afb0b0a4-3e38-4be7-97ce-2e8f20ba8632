import { pgTable, varchar, timestamp, bigint } from "drizzle-orm/pg-core";

export const betaEmails = pgTable("beta_emails", {
  email: varchar({ length: 255 }).primaryKey().notNull(),
  // You can use { mode: "bigint" } if numbers are exceeding js number limitations
  addedBy: bigint("added_by", { mode: "number" }).notNull(),
  // You can use { mode: "bigint" } if numbers are exceeding js number limitations
  deletedBy: bigint("deleted_by", { mode: "number" }),
  // You can use { mode: "bigint" } if numbers are exceeding js number limitations
  claimedBy: bigint("claimed_by", { mode: "number" }),
  dateCreated: timestamp("date_created", { mode: "string" }).notNull(),
  dateDeleted: timestamp("date_deleted", { mode: "string" }),
});
