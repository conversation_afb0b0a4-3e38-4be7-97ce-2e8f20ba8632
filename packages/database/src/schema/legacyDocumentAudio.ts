import {
  pgTable,
  varchar,
  index,
  unique,
  serial,
  timestamp,
  foreignKey,
  bigint,
  json,
  text,
} from "drizzle-orm/pg-core";
import { users } from "./users";
import { documents } from "./documents";
import { ttsVoices } from "./ttsVoices";
import { relations } from "drizzle-orm";

export const legacyDocumentAudio = pgTable(
  "legacy_document_audio",
  {
    id: serial().primaryKey().notNull(),
    uuid: varchar({ length: 255 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    documentId: bigint("document_id", { mode: "number" }).notNull(),
    sectionId: varchar("section_id", { length: 255 }).notNull(),
    paragraphId: varchar("paragraph_id", { length: 255 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    ownerUserId: bigint("owner_user_id", { mode: "number" }).notNull(),
    externalTaskId: varchar("external_task_id", { length: 255 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    ttsVoiceId: bigint("tts_voice_id", { mode: "number" }).notNull(),
    name: varchar({ length: 255 }),
    type: varchar({ length: 255 }),
    path: text(),
    status: varchar({ length: 255 }).notNull(),
    dateCreated: timestamp("date_created", { mode: "string" }),
    dateModified: timestamp("date_modified", { mode: "string" }),
    dateDeleted: timestamp("date_deleted", { mode: "string" }),
    fileMetadata: json("file_metadata"),
  },
  (table) => [
    index("document_audio_document_id").using(
      "btree",
      table.documentId.asc().nullsLast().op("int8_ops")
    ),
    index("document_audio_external_task_id").using(
      "btree",
      table.externalTaskId.asc().nullsLast().op("text_ops")
    ),
    index("document_audio_owner_user_id").using(
      "btree",
      table.ownerUserId.asc().nullsLast().op("int8_ops")
    ),
    index("document_audio_paragraph_id").using(
      "btree",
      table.paragraphId.asc().nullsLast().op("text_ops")
    ),
    index("document_audio_section_id").using(
      "btree",
      table.sectionId.asc().nullsLast().op("text_ops")
    ),
    foreignKey({
      columns: [table.documentId],
      foreignColumns: [documents.id],
      name: "document_audio_document_id_fkey",
    }).onDelete("cascade"),
    foreignKey({
      columns: [table.ownerUserId],
      foreignColumns: [users.id],
      name: "document_audio_user_id_fkey",
    }).onDelete("cascade"),
    foreignKey({
      columns: [table.ttsVoiceId],
      foreignColumns: [ttsVoices.id],
      name: "document_audio_tts_voice_id_fkey",
    }).onDelete("cascade"),
    unique("document_audio_uuid_key").on(table.uuid),
  ]
);

export const legacyDocumentAudioRelations = relations(
  legacyDocumentAudio,
  ({ one }) => ({
    document: one(documents, {
      fields: [legacyDocumentAudio.documentId],
      references: [documents.id],
    }),
    user: one(users, {
      fields: [legacyDocumentAudio.ownerUserId],
      references: [users.id],
    }),
    ttsVoice: one(ttsVoices, {
      fields: [legacyDocumentAudio.ttsVoiceId],
      references: [ttsVoices.id],
    }),
  })
);
