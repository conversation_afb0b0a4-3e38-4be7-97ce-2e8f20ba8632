import {
  pgTable,
  varchar,
  index,
  unique,
  serial,
  timestamp,
  foreignKey,
  bigint,
  json,
} from "drizzle-orm/pg-core";
import { users } from "./users";
import { documents } from "./documents";
import { relations } from "drizzle-orm";

export const documentHighlights = pgTable(
  "document_highlights",
  {
    id: serial().primaryKey().notNull(),
    uuid: varchar({ length: 255 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    documentId: bigint("document_id", { mode: "number" }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    ownerUserId: bigint("owner_user_id", { mode: "number" }).notNull(),
    meta: json().notNull(),
    dateCreated: timestamp("date_created", { mode: "string" }),
    dateModified: timestamp("date_modified", { mode: "string" }),
  },
  (table) => [
    index("document_highlights_document_id").using(
      "btree",
      table.documentId.asc().nullsLast().op("int8_ops")
    ),
    index("document_highlights_owner_user_id").using(
      "btree",
      table.ownerUserId.asc().nullsLast().op("int8_ops")
    ),
    foreignKey({
      columns: [table.documentId],
      foreignColumns: [documents.id],
      name: "document_highlights_document_id_fkey",
    }),
    foreignKey({
      columns: [table.ownerUserId],
      foreignColumns: [users.id],
      name: "document_highlights_user_id_fkey",
    }),
    unique("document_highlights_uuid_key").on(table.uuid),
  ]
);

export const documentHighlightsRelations = relations(
  documentHighlights,
  ({ one }) => ({
    document: one(documents, {
      fields: [documentHighlights.documentId],
      references: [documents.id],
    }),
    user: one(users, {
      fields: [documentHighlights.ownerUserId],
      references: [users.id],
    }),
  })
);
