import {
  pgTable,
  varchar,
  index,
  unique,
  serial,
  timestamp,
} from "drizzle-orm/pg-core";
import { relations, sql } from "drizzle-orm";
import { userRoles } from "./userRoles";
import { documentAudio } from "./documentAudio";
import { documentEmbeddings } from "./documentEmbeddings";
import { documentHighlights } from "./documentHighlights";
import { documents } from "./documents";
import { documentUsers } from "./documentUsers";
import { legacyDocumentAudio } from "./legacyDocumentAudio";
import { sectionSummary } from "./sectionSummary";
import { tags } from "./tags";

export const users = pgTable(
  "users",
  {
    id: serial().primaryKey().notNull(),
    estendioId: varchar("estendio_id", { length: 255 }),
    role: varchar({ length: 255 }).default(sql`NULL`),
    createdAt: timestamp("created_at", { mode: "string" }).notNull(),
    modifiedAt: timestamp("modified_at", { mode: "string" }),
    deletedAt: timestamp("deleted_at", { mode: "string" }),
  },
  (table) => [
    index("user_estendio_id_index").using(
      "btree",
      table.estendioId.asc().nullsLast().op("text_ops")
    ),
    unique("users_estendio_id_key").on(table.estendioId),
  ]
);

export const usersRelations = relations(users, ({ many }) => ({
  userRoles: many(userRoles),
  documentUsers: many(documentUsers),
  documents: many(documents),
  documentEmbeddings: many(documentEmbeddings),
  legacyDocumentAudios: many(legacyDocumentAudio),
  sectionSummaries: many(sectionSummary),
  documentAudios: many(documentAudio),
  documentHighlights: many(documentHighlights),
  tags: many(tags),
}));

export type User = typeof users.$inferSelect; // For selecting
export type NewUser = typeof users.$inferInsert; // For inserting
