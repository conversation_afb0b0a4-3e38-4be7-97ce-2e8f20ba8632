import {
  pgTable,
  varchar,
  index,
  unique,
  serial,
  timestamp,
  foreignKey,
  bigint,
} from "drizzle-orm/pg-core";
import { tags } from "./tags";
import { relations } from "drizzle-orm";

export const taggables = pgTable(
  "taggables",
  {
    id: serial().primaryKey().notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    tagId: bigint("tag_id", { mode: "number" }).notNull(),
    taggableType: varchar("taggable_type", { length: 255 }).notNull(),
    // You can use { mode: "bigint" } if numbers are exceeding js number limitations
    taggableId: bigint("taggable_id", { mode: "number" }).notNull(),
    createdAt: timestamp("created_at", { mode: "string" })
      .defaultNow()
      .notNull(),
  },
  (table) => [
    index("taggables_taggable_types_index").using(
      "btree",
      table.taggableType.asc().nullsLast().op("text_ops"),
      table.taggableId.asc().nullsLast().op("int8_ops")
    ),
    foreignKey({
      columns: [table.tagId],
      foreignColumns: [tags.id],
      name: "taggables_tag_id_foreign_key",
    }).onDelete("cascade"),
    unique("unique_taggable_item").on(
      table.tagId,
      table.taggableType,
      table.taggableId
    ),
  ]
);

export const taggablesRelations = relations(taggables, ({ one }) => ({
  tag: one(tags, {
    fields: [taggables.tagId],
    references: [tags.id],
  }),
}));
