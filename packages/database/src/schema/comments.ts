import {
  pgTable,
  varchar,
  integer,
  index,
  unique,
  serial,
  timestamp,
  foreignKey,
  text,
} from "drizzle-orm/pg-core";
import { commentThreads } from "./commentThreads";
import { relations } from "drizzle-orm";

export const comments = pgTable(
  "comments",
  {
    id: serial().primaryKey().notNull(),
    uuid: varchar({ length: 255 }).notNull(),
    threadId: integer("thread_id").notNull(),
    ownerUserId: integer("owner_user_id").notNull(),
    text: text().notNull(),
    createdAt: timestamp("created_at", { mode: "string" })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp("updated_at", { mode: "string" })
      .defaultNow()
      .notNull(),
    deletedAt: timestamp("deleted_at", { mode: "string" }),
  },
  (table) => [
    index().using("btree", table.ownerUserId.asc().nullsLast().op("int4_ops")),
    index().using("btree", table.threadId.asc().nullsLast().op("int4_ops")),
    index().using("btree", table.uuid.asc().nullsLast().op("text_ops")),
    foreignKey({
      columns: [table.threadId],
      foreignColumns: [commentThreads.id],
      name: "comments_thread_id_fkey",
    }),
    unique("comments_uuid_key").on(table.uuid),
  ]
);

export const commentsRelations = relations(comments, ({ one }) => ({
  commentThread: one(commentThreads, {
    fields: [comments.threadId],
    references: [commentThreads.id],
  }),
}));
