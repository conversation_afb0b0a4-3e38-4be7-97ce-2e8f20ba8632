import path from "node:path";
import * as fs from "fs/promises";
import { dbClient } from "./db";
import { migrate as localMigrate } from "drizzle-orm/node-postgres/migrator";
import { migrate as awsMigrate } from "drizzle-orm/aws-data-api/pg/migrator";
import { NodePgDatabase } from "drizzle-orm/node-postgres";
import { AwsDataApiPgDatabase } from "drizzle-orm/aws-data-api/pg";
import { fileURLToPath } from "node:url";
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export async function runMigration() {
  const { db, dialect, pool } = await dbClient;

  const migrationsPath = path.join(__dirname, "migrations");
  console.log("Starting migration...");
  if (dialect == "node-postgres") {
    if (!pool) {
      console.error("Error: No local pool setup. Exiting...");
      return;
    }
    if (!(db instanceof NodePgDatabase)) {
      console.error("Error: Not using Node Postgres database driver");
      return;
    }

    const test = await fs.readdir(migrationsPath);
    console.log(test);

    await localMigrate(db, {
      migrationsFolder: migrationsPath,
    });
  } else if (dialect == "aws-data-api-pg") {
    if (!(db instanceof AwsDataApiPgDatabase)) {
      console.error("Error: Not using AWS Data API Postgres database driver");
      return;
    }

    await awsMigrate(db, { migrationsFolder: migrationsPath });
  } else {
    console.error("Invalid dialect type. Exiting...");
    return;
  }

  console.log("Migration finished!");
}
