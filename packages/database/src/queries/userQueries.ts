import { eq } from "drizzle-orm";
import { dbClient } from "../db";
import { users, User } from "../schema";

async function getDbInstance() {
  const { db } = await dbClient;
  return db;
}

export async function findUserByEstendioId(
  id: string
): Promise<User | undefined> {
  const db = await getDbInstance();
  return await db.query.users
    .findFirst({
      where: eq(users.estendioId, id),
    })
    .execute();
}
