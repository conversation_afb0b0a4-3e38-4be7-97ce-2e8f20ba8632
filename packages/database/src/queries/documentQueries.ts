import { eq } from "drizzle-orm";
import { getTableColumns } from "drizzle-orm";
import { dbClient } from "../db";
import { Document, documents, documentUsers, User } from "../schema";

async function getDbInstance() {
  const { db } = await dbClient;
  return db;
}

export async function findDocumentByUUID(
  uuid: string
): Promise<Document | undefined> {
  const db = await getDbInstance();
  return await db.query.documents
    .findFirst({
      where: eq(documents.uuid, uuid),
    })
    .execute();
}

export async function getUserDocuments(user: User): Promise<Document[]> {
  const db = await getDbInstance();
  return await db
    .select(getTableColumns(documents))
    .from(documents)
    .innerJoin(documentUsers, eq(documentUsers.documentId, documents.id))
    .where(eq(documentUsers.userId, user.id))
    .execute();
}
