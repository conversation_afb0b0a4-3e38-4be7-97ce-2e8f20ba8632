{"name": "@another-nestjs-project/database", "version": "0.0.1", "private": true, "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./queries/users": {"import": "./dist/queries/userQueries.js", "types": "./dist/queries/userQueries.d.ts"}, "./queries/documents": {"import": "./dist/queries/documentQueries.js", "types": "./dist/queries/documentQueries.d.ts"}, "./queries": {"import": "./dist/queries/index.js", "types": "./dist/queries/index.d.ts"}, "./schema": {"import": "./dist/schema/index.js", "types": "./dist/schema/index.d.ts"}}, "scripts": {"build": "tsc -p tsconfig.json", "dev": "tsc -p tsconfig.json --watch"}, "dependencies": {"@aws-sdk/client-rds-data": "^3.812.0", "@types/node": "^22.15.20", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "pg": "^8.16.0"}, "devDependencies": {"@types/pg": "^8.15.2", "drizzle-kit": "^0.31.1", "tsx": "^4.19.4"}}