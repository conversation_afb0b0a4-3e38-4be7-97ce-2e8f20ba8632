{"extends": "../../tsconfig.json", "compilerOptions": {"composite": true, "outDir": "./dist", "rootDir": "./src", "module": "ES2022", "target": "ES2022", "declaration": true, "declarationMap": true, "sourceMap": true, "strict": false, "esModuleInterop": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}