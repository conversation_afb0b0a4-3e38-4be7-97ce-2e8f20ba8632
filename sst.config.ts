/// <reference path="./.sst/platform/config.d.ts" />

export default $config({
  app(input) {
    return {
      name: "prototype-nestjs-project",
      removal: input?.stage === "production" ? "retain" : "remove",
      protect: ["production"].includes(input?.stage),
      home: "aws",
    };
  },
  async run() {
    const nestFunc = new sst.aws.Function("nestjsfunction", {
      handler: "packages/api/src/lambda.handler",
      runtime: "nodejs20.x",
      nodejs: {
        format: "esm",
        install: [
          "@nestjs/common",
          "@nestjs/core",
          "@nestjs/config",
          "class-validator",
          "class-transformer",
          "@nestjs/platform-express",
          "reflect-metadata",
          "nestjs-pino",
          "pino",
          "pino-http",
        ],
        esbuild: {
          packages: "external",
          keepNames: true,
          sourcemap: true,
          target: "es2022",
          format: "esm",
        },
      },
      url: {
        cors: true,
      },
      environment: {
        NODE_ENV: $app.stage === "prod" ? "production" : "development",
        DB_CONNECTION_TYPE: process.env.DB_CONNECTION_TYPE,
        RDS_AWS_REGION: process.env.RDS_AWS_REGION,
        RDS_RESOURCE_ARN: process.env.RDS_RESOURCE_ARN,
        RDS_SECRET_ARN: process.env.RDS_SECRET_ARN,
        RDS_DATABASE_NAME: process.env.RDS_DATABASE_NAME,
        KMS_ARN: process.env.KMS_ARN!,
      },
      timeout: "20 seconds",
      permissions: [
        {
          actions: [
            "kms:GetPublicKey",
            "kms:Verify",
          ],
          resources: [process.env.KMS_ARN || "*"],
        },
        {
          actions: [
            "rds-data:ExecuteStatement",
            "rds-data:BatchExecuteStatement",
            "rds-data:BeginTransaction",
            "rds-data:CommitTransaction",
            "rds-data:RollbackTransaction",
          ],
          resources: [process.env.RDS_RESOURCE_ARN || "*"],
        },
        {
          actions: [
            "secretsmanager:GetSecretValue",
            "secretsmanager:DescribeSecret",
          ],
          resources: [process.env.RDS_SECRET_ARN || "*"],
        },
      ],
    });

    return {
      nestFunc: nestFunc.url,
    };
  },
});
