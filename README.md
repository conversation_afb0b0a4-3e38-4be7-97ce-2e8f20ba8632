# NestJS SST V3 implementation

I tried, we have a simple nestjs project being ran inside a lambda function.

## Setup

In order to get this working you should ensure you've got your aws cli configured and pointing to the right area.

Once configured in the root of the project you should run the following:

1. `make setup`
2. `make run`

## Creating NestJS items

### Controllers

`make api/controller/create name=<value>`
name values should be lowercase

### Services

`make api/service/create name=<value>`
name values should be lowercase

## Makefile help

If you want to see a list of what commands are available within the Makefile then run `make help` and all commands should be listed for you

## Issues

In order to get this working I had to explicitly call `@Inject(AppService)` on the `AppController` area. Without this the `AppService` class was not injected by NestJS. I don't know why this needed to be done and so needs investigating. Must be something wrong with the ESBuild configuration
