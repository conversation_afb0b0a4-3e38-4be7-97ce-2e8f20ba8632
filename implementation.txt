This is a nest API which is deployed by SST v3. A Postgres database has its schema defined and access via an ORM. JWT is used for maintaining authentication.

Features
* Uses a configuration service with validation (NestJS ConfigModule) which gives a single place to pick up from .env
* Proper IAM least privilege permissions
* Comprehensive error handling and logging
* Standardized on ESM consistently
* Uses database connection pooling and retry logic
