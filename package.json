{"name": "another-nestjs-project", "version": "1.0.0", "main": "index.js", "license": "MIT", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "sst build", "build:api": "cd packages/api && yarn build", "console": "sst console", "database:start": "docker-compose -f local/docker-compose.yml up -d", "database:stop": "docker-compose -f local/docker-compose.yml stop", "db": "sst shell drizzle-kit", "db:studio": "sst shell drizzle-kit studio ", "deploy": "sst deploy", "dev": "sst dev", "dev:fe": "sst dev next dev packages/frontend", "remove": "sst remove", "typecheck": "tsc --noEmit"}, "dependencies": {"class-validator": "^0.14.2", "sst": "3.14.25"}, "devDependencies": {"@types/aws-lambda": "8.10.149"}}