# =========================================================================== #
# HELPERS
# =========================================================================== #

.PHONY: help
help:
	@echo 'Usage:'
	@sed -n 's/^##//p' ${MAKEFILE_LIST} | column -t -s ':' | sed -e 's/^/ /'

.PHONY: confirm
confirm:
	@echo -n 'Are you sure? [y/N] ' && read ans && [ $${ans:-N} = y ]

# =========================================================================== #
# DEVELOPMENT
# =========================================================================== #

## setup: Set's up the project 
.PHONY: setup
setup:
	@echo 'Setting up project...'
	yarn install
	yarn workspace @another-nestjs-project/database build 	
	yarn workspace @another-nestjs-project/api build 
	@echo 'Project set up. Try running dev mode with `make run`'

## cache/clear/sst: Remove SST build cache 
.PHONY: cache/clear/sst
cache/clear/sst:
	@echo 'Removing SST cache...'
	@rm -rf .sst
	@echo 'SST cache cleared'

## cache/clear/nest: Remove NestJS build cache 
.PHONY: cache/clear/nest
cache/clear/nest:
	@echo 'Removing NestJS cache...'
	@rm -rf packages/api/dist
	@echo 'NestJS cache cleared'

## cache/clear/all: Remove .sst and dist directories for a fresh start
.PHONY: cache/clear/all
cache/clear/all: cache/clear/sst cache/clear/nest

## run: Runs the dev instance of the project
.PHONY: run
run:
	@echo 'Starting dev mode...'
	npx sst dev

## deploy/push: Deploys project to 'prototype' stage
.PHONY: deploy/push
deploy/push:
	@echo 'Deploying project to AWS...'
	npx sst deploy --stage='prototype'
	@echo 'Project deployed!'

## deploy/drop: Removes the deployed 'prototpe' stage
.PHONY: deploy/drop
deploy/drop:
	@echo 'Removing deployed project from AWS...'
	npx sst remove --stage='prototype'

## api/build: Builds the API package
.PHONY: api/build
api/build:
	@echo 'Building API...'
	@cd packages/api && yarn build
	@echo 'API built!'

.PHONY: api/packages/add
api/packages/add:
	@echo 'Adding ${name}'
	yarn workspace @another-nestjs-project/api add ${name}
	@echo '${name} package added to api'

## api/controller/create name=$1: create a controller with the specified name
.PHONY: api/controller/create
api/controller/create:
	@echo 'Creating controller ${name}'
	@if command -v nest > /dev/null; then \
			cd packages/api; \
			nest generate controller ${name}; \
	else \
			read -p "Nest CLI is not installed on your machine. Do you want to install it? [Y/n] " choice; \
			if [ "$$choice" != "n" ] && [ "$$choice" != "N" ]; then \
					npm i -g @nestjs/cli; \
					cd packages/api; \
					nest generate controller ${name}; \
			else \
					echo "You chose not to install NestJS you FOOL!. Go away..."; \
					exit 1; \
			fi; \
	fi

# TODO: How can I do these command exists checks without copy pasta?
## api/service/create name=$1: create a service with the specified name
.PHONY: api/service/create
api/service/create:
	@echo 'Creating provider ${name}'
	@if command -v nest > /dev/null; then \
			cd packages/api; \
			nest generate service ${name}; \
	else \
			read -p "Nest CLI is not installed on your machine. Do you want to install it? [Y/n] " choice; \
			if [ "$$choice" != "n" ] && [ "$$choice" != "N" ]; then \
					npm i -g @nestjs/cli; \
					cd packages/api; \
					nest generate service ${name}; \
			else \
					echo "You chose not to install NestJS you FOOL!. Go away..."; \
					exit 1; \
			fi; \
	fi

.PHONY: database/build
database/build:
	@echo 'Building database package...'
	rm -rf ./packages/database/dist
	rm ./packages/database/tsconfig.tsbuildinfo
	yarn workspace @another-nestjs-project/database build 
	cp -R ./packages/database/src/migrations ./packages/database/dist/migrations
	@echo 'Database package built'

.PHONY: database/packages/add
database/packages/add:
	@echo 'Adding ${name} to database package...'
	yarn workspace @another-nestjs-project/database add ${name}
	@echo '${name} package added to api'


.PHONY: scripts/packages/add
scripts/packages/add:
	@echo 'Adding ${name}'
	yarn workspace @another-nestjs-project/scripts add ${name}
	@echo '${name} package added to scripts'


.PHONY: database/migrate 
database/migrate:
	@echo 'Starting migration....'
	yarn workspace @another-nestjs-project/scripts shell src/runMigrate.ts